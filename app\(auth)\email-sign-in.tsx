// Email Sign In Screen for IraChat
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { useDispatch } from 'react-redux';
import { signInUser } from '../../src/services/authService';
import { emailAuthService } from '../../src/services/emailAuthService';
import { setUser } from '../../src/redux/userSlice';
import { AuthCredentials } from '../../src/types';
import { AnimatedButton } from '../../src/components/ui/AnimatedButton';
import { IraChatWallpaper } from '../../src/components/ui/IraChatWallpaper';
import { IRACHAT_COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS, SHADOWS } from '../../src/styles/iraChatDesignSystem';

const EmailSignInScreen: React.FC = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);

  const validateForm = () => {
    if (!formData.email.trim()) {
      Alert.alert('Validation Error', 'Please enter your email address');
      return false;
    }

    if (!formData.email.includes('@')) {
      Alert.alert('Validation Error', 'Please enter a valid email address');
      return false;
    }

    if (!formData.password.trim()) {
      Alert.alert('Validation Error', 'Please enter your password');
      return false;
    }

    if (formData.password.length < 6) {
      Alert.alert('Validation Error', 'Password must be at least 6 characters');
      return false;
    }

    return true;
  };

  const handleResendVerification = async () => {
    if (!formData.email || !formData.password) {
      Alert.alert('Error', 'Please enter your email and password first.');
      return;
    }

    setIsLoading(true);
    try {
      const result = await emailAuthService.resendVerificationEmail(
        formData.email.trim().toLowerCase(),
        formData.password
      );

      Alert.alert(
        result.success ? 'Email Sent' : 'Error',
        result.message,
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to resend verification email.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignIn = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      const credentials: AuthCredentials = {
        email: formData.email.trim().toLowerCase(),
        password: formData.password,
        method: 'email',
      };

      const result = await signInUser(credentials);

      if (result.success && result.user) {
        dispatch(setUser(result.user));
        Alert.alert(
          'Welcome Back!',
          'You have successfully signed in to IraChat.',
          [
            {
              text: 'Continue',
              onPress: () => router.replace('/(tabs)'),
            },
          ]
        );
      } else {
        // Check if it's an email verification issue
        if (result.requiresVerification) {
          Alert.alert(
            'Email Not Verified',
            result.message || 'Please verify your email before signing in.',
            [
              {
                text: 'Resend Email',
                onPress: () => handleResendVerification(),
              },
              {
                text: 'OK',
                style: 'cancel',
              },
            ]
          );
        } else {
          Alert.alert('Sign In Failed', result.message || 'Invalid email or password');
        }
      }
    } catch (error: any) {
      console.error('❌ Email sign in error:', error);
      Alert.alert('Error', 'Failed to sign in. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor={IRACHAT_COLORS.primary} />
      <IraChatWallpaper />
      
      <SafeAreaView style={styles.safeArea}>
        <KeyboardAvoidingView 
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardView}
        >
          <ScrollView 
            contentContainerStyle={styles.scrollContent}
            keyboardShouldPersistTaps="handled"
          >
            {/* Header */}
            <View style={styles.header}>
              <TouchableOpacity 
                style={styles.backButton}
                onPress={() => router.back()}
                disabled={isLoading}
              >
                <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
              </TouchableOpacity>
              
              <View style={styles.headerContent}>
                <Text style={styles.headerTitle}>Sign In</Text>
                <Text style={styles.headerSubtitle}>Welcome back to IraChat</Text>
              </View>
            </View>

            {/* Form */}
            <View style={styles.formContainer}>
              {/* Email Input */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Email Address</Text>
                <View style={styles.inputWrapper}>
                  <Ionicons name="mail" size={20} color="rgba(255, 255, 255, 0.7)" style={styles.inputIcon} />
                  <TextInput
                    style={styles.textInput}
                    placeholder="Enter your email"
                    placeholderTextColor="rgba(255, 255, 255, 0.5)"
                    value={formData.email}
                    onChangeText={(text) => setFormData(prev => ({ ...prev, email: text }))}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    autoCorrect={false}
                    editable={!isLoading}
                  />
                </View>
              </View>

              {/* Password Input */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Password</Text>
                <View style={styles.inputWrapper}>
                  <Ionicons name="lock-closed" size={20} color="rgba(255, 255, 255, 0.7)" style={styles.inputIcon} />
                  <TextInput
                    style={[styles.textInput, { flex: 1 }]}
                    placeholder="Enter your password"
                    placeholderTextColor="rgba(255, 255, 255, 0.5)"
                    value={formData.password}
                    onChangeText={(text) => setFormData(prev => ({ ...prev, password: text }))}
                    secureTextEntry={!showPassword}
                    autoCapitalize="none"
                    autoCorrect={false}
                    editable={!isLoading}
                  />
                  <TouchableOpacity
                    onPress={() => setShowPassword(!showPassword)}
                    style={styles.passwordToggle}
                    disabled={isLoading}
                  >
                    <Ionicons 
                      name={showPassword ? "eye-off" : "eye"} 
                      size={20} 
                      color="rgba(255, 255, 255, 0.7)" 
                    />
                  </TouchableOpacity>
                </View>
              </View>

              {/* Sign In Button */}
              <TouchableOpacity
                style={[styles.signInButton, isLoading && styles.signInButtonDisabled]}
                onPress={handleSignIn}
                disabled={isLoading}
              >
                {!isLoading && <Ionicons name="log-in" size={20} color="#FFFFFF" style={styles.buttonIcon} />}
                <Text style={styles.buttonText}>
                  {isLoading ? "Signing In..." : "Sign In"}
                </Text>
              </TouchableOpacity>

              {/* Loading Indicator */}
              {isLoading && (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="small" color="#FFFFFF" />
                  <Text style={styles.loadingText}>Signing you in...</Text>
                </View>
              )}
            </View>

            {/* Footer Links */}
            <View style={styles.footerContainer}>
              {/* Forgot Password */}
              <TouchableOpacity 
                style={styles.forgotPasswordContainer}
                disabled={isLoading}
              >
                <Text style={styles.forgotPasswordText}>Forgot your password?</Text>
              </TouchableOpacity>

              {/* Alternative Sign In */}
              <View style={styles.alternativeContainer}>
                <Text style={styles.alternativeText}>Or sign in with</Text>
                <TouchableOpacity
                  style={styles.phoneButton}
                  onPress={() => router.push('/(auth)/phone-sign-in')}
                  disabled={isLoading}
                >
                  <Ionicons name="call" size={20} color="#007AFF" />
                  <Text style={styles.phoneButtonText}>Phone Number</Text>
                </TouchableOpacity>
              </View>

              {/* Create Account Link */}
              <View style={styles.createAccountContainer}>
                <Text style={styles.createAccountText}>Don't have an account? </Text>
                <TouchableOpacity 
                  onPress={() => router.push('/(auth)/email-register')}
                  disabled={isLoading}
                >
                  <Text style={styles.createAccountLink}>Create Account</Text>
                </TouchableOpacity>
              </View>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: IRACHAT_COLORS.primary,
  },
  safeArea: {
    flex: 1,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: SPACING.lg,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: SPACING.md,
    marginBottom: SPACING.xl,
  },
  backButton: {
    padding: SPACING.sm,
    marginRight: SPACING.md,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: TYPOGRAPHY.fontSize['2xl'],
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontFamily: TYPOGRAPHY.fontFamilyBold,
  },
  headerSubtitle: {
    fontSize: TYPOGRAPHY.fontSize.base,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  formContainer: {
    flex: 1,
    gap: SPACING.lg,
  },
  inputContainer: {
    marginBottom: SPACING.md,
  },
  inputLabel: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: '#FFFFFF',
    fontWeight: '600',
    marginBottom: SPACING.xs,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: SPACING.md,
    height: 56,
  },
  inputIcon: {
    marginRight: SPACING.sm,
  },
  textInput: {
    flex: 1,
    fontSize: TYPOGRAPHY.fontSize.base,
    color: '#FFFFFF',
    height: '100%',
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  passwordToggle: {
    padding: SPACING.xs,
  },
  signInButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: BORDER_RADIUS.md,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    marginTop: SPACING.md,
    minHeight: 50,
  },
  signInButtonDisabled: {
    opacity: 0.6,
  },
  buttonIcon: {
    marginRight: SPACING.sm,
  },
  buttonText: {
    fontSize: TYPOGRAPHY.fontSize.base,
    color: '#FFFFFF',
    fontWeight: '600',
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: SPACING.sm,
  },
  loadingText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: 'rgba(255, 255, 255, 0.8)',
    marginLeft: SPACING.sm,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  footerContainer: {
    paddingTop: SPACING.xl,
    paddingBottom: SPACING.lg,
  },
  forgotPasswordContainer: {
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  forgotPasswordText: {
    fontSize: TYPOGRAPHY.fontSize.base,
    color: 'rgba(255, 255, 255, 0.8)',
    textDecorationLine: 'underline',
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  alternativeContainer: {
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  alternativeText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: 'rgba(255, 255, 255, 0.6)',
    marginBottom: SPACING.sm,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  phoneButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.sm,
    gap: SPACING.xs,
  },
  phoneButtonText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: '#007AFF',
    fontWeight: '600',
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  createAccountContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  createAccountText: {
    fontSize: TYPOGRAPHY.fontSize.base,
    color: 'rgba(255, 255, 255, 0.8)',
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  createAccountLink: {
    fontSize: TYPOGRAPHY.fontSize.base,
    color: '#FFFFFF',
    fontWeight: 'bold',
    textDecorationLine: 'underline',
    fontFamily: TYPOGRAPHY.fontFamily,
  },
});

export default EmailSignInScreen;
