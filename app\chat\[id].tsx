import { useLoc<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import { View, ActivityIndicator, Alert, Text, TouchableOpacity, StyleSheet } from "react-native";
import { StatusBar } from 'expo-status-bar';
import { useSelector } from "react-redux";
import { Ionicons } from "@expo/vector-icons";
import { RootState } from "../../src/redux/store";
import { navigationService } from "../../src/services/navigationService";
import { UltimateIndividualChatRoom } from "../../src/components/UltimateIndividualChatRoom";
import { UltimateGroupChatRoom } from "../../src/components/UltimateGroupChatRoom";
import { realChatService } from "../../src/services/realChatService";
import { localChatManagementService } from "../../src/services/localChatManagementService";
import { ResponsiveContainer } from "../../src/components/ui/ResponsiveContainer";
import { IRACHAT_COLORS, TYPOGRAPHY } from "../../src/styles/iraChatDesignSystem";
import { ResponsiveTypography, ResponsiveSpacing } from "../../src/utils/responsiveUtils";

interface ChatData {
  id: string;
  name: string;
  avatar?: string;
  isGroup: boolean;
  isOnline?: boolean;
  lastSeen?: Date;
  participantIds: string[];
}

export default function ChatScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const currentUser = useSelector((state: RootState) => state.user.currentUser);
  
  const [chatData, setChatData] = useState<ChatData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Helper function to save chat data to local storage
  const saveToLocalStorage = async (data: ChatData) => {
    try {
      await localChatManagementService.saveChat({
        id: data.id,
        name: data.name,
        avatar: data.avatar,
        lastMessage: '', // Will be updated when messages are loaded
        timestamp: new Date(),
        messageCount: 0,
        mediaCount: 0,
        isGroup: data.isGroup,
        participants: data.participantIds,
        isArchived: false,
        isMuted: false,
        isPinned: false,
        isLocked: false,
        isHidden: false,
        isDeleted: false,
      });
      console.log('✅ Chat saved to local storage:', data.id);
    } catch (error) {
      console.error('❌ Failed to save chat to local storage:', error);
    }
  };

  // Helper function to sync with Firebase in background
  const syncWithFirebaseInBackground = async (chatId: string, userId: string) => {
    try {
      console.log('🔄 Background sync with Firebase for chat:', chatId);
      // This runs in background and doesn't block the UI
      const result = await realChatService.getUserChats(userId);
      if (result.success && result.chats) {
        const chat = result.chats.find(c => c.id === chatId);
        if (chat) {
          // Update local storage with latest Firebase data
          await saveToLocalStorage({
            id: chat.id,
            name: chat.isGroup ? (chat.groupName || 'Group Chat') : (chat.participantName || 'Unknown User'),
            avatar: chat.isGroup ? chat.groupAvatar : chat.participantAvatar,
            isGroup: chat.isGroup || false,
            isOnline: true,
            participantIds: chat.participants || [],
          });
        }
      }
    } catch (error) {
      console.log('⚠️ Background sync failed (this is okay):', error);
    }
  };

  // Show alert when there's an error - moved to top level
  React.useEffect(() => {
    if (error) {
      Alert.alert(
        'Error',
        error,
        [
          { text: 'Go Back', onPress: () => navigationService.goBack() },
          { text: 'Retry', onPress: loadChatData },
        ]
      );
    }
  }, [error]);

  useEffect(() => {
    if (!id || !currentUser?.id) {
      setError("Invalid chat ID or user not authenticated");
      setLoading(false);
      return;
    }

    loadChatData();
  }, [id, currentUser]);

  const loadChatData = async () => {
    if (!id || !currentUser?.id) return;

    setLoading(true);
    setError(null);

    try {
      console.log('📱 Loading chat data for chat:', id);

      // Initialize local chat management service
      await localChatManagementService.initialize();

      // First, try to load from local storage
      const localChats = await localChatManagementService.getAllChats();
      const localChat = localChats.find((c: any) => c.id === id);

      if (localChat) {
        console.log('✅ Found chat in local storage:', localChat.name);
        setChatData({
          id: localChat.id,
          name: localChat.name,
          avatar: localChat.avatar,
          isGroup: localChat.isGroup,
          isOnline: true, // Default to online for local chats
          participantIds: localChat.participants,
        });

        // Try to sync with Firebase in the background (non-blocking)
        syncWithFirebaseInBackground(id, currentUser.id);
        return;
      }

      console.log('📡 Chat not found locally, checking if this is an IraChat contact...');

      // ONLY create new chat for IraChat contacts with short IDs (contact IDs, not Firebase user IDs)
      // Also include mock contacts for testing
      if (id.length < 25 || id.startsWith('mock-')) {
        console.log('🆕 Creating new chat for IraChat contact:', id);

        // Create a new chat for this IraChat contact (offline-first)
        const newChatData = {
          id: `chat_${currentUser.id}_${id}_${Date.now()}`,
          name: 'New Chat', // Will be updated when we get contact info
          avatar: '',
          isGroup: false,
          isOnline: false, // Start as offline-capable
          participantIds: [currentUser.id, id],
        };

        // Save to local storage immediately
        await localChatManagementService.saveChat({
          id: newChatData.id,
          name: newChatData.name,
          avatar: newChatData.avatar,
          isGroup: newChatData.isGroup,
          participants: newChatData.participantIds,
          lastMessage: '',
          timestamp: new Date(),
          messageCount: 0,
          mediaCount: 0,
        });

        setChatData(newChatData);
        console.log('✅ New chat created automatically for IraChat contact');

        // Try to sync with Firebase in background (non-blocking)
        syncWithFirebaseInBackground(id, currentUser.id);
        return;
      }

      // For longer IDs (Firebase user IDs), try Firebase first
      console.log('📡 Long ID detected, trying Firebase for existing chat...');

      try {
        const result = await realChatService.getUserChats(currentUser.id);

        if (result.success && result.chats) {
          const chat = result.chats.find(c => c.id === id);

          if (chat) {
            const chatData = {
              id: chat.id,
              name: chat.isGroup ? (chat.groupName || 'Group Chat') : (chat.participantName || 'Unknown User'),
              avatar: chat.isGroup ? chat.groupAvatar : chat.participantAvatar,
              isGroup: chat.isGroup || false,
              isOnline: true,
              participantIds: chat.participants || [],
            };

            setChatData(chatData);

            // Save to local storage for future offline access
            await saveToLocalStorage(chatData);
            return;
          }
        }

        // If Firebase fails or chat not found, create offline fallback for long IDs
        console.log('⚠️ Firebase unavailable or chat not found, creating offline fallback');
        const fallbackChatData = {
          id: id,
          name: 'Chat (Offline)',
          avatar: '',
          isGroup: false,
          isOnline: false,
          participantIds: [currentUser.id],
        };

        setChatData(fallbackChatData);
        console.log('✅ Offline fallback chat created');

      } catch (firebaseError) {
        console.log('⚠️ Firebase error (expected in offline mode):', firebaseError);

        // Create offline fallback when Firebase fails
        const fallbackChatData = {
          id: id,
          name: 'Chat (Offline)',
          avatar: '',
          isGroup: false,
          isOnline: false,
          participantIds: [currentUser.id],
        };

        setChatData(fallbackChatData);
        console.log('✅ Offline fallback chat created due to Firebase error');
      }

    } catch (error) {
      console.error('Error loading chat data:', error);

      // Even if everything fails, create a basic offline chat
      console.log('🔄 Creating basic offline chat as final fallback');
      const basicChatData = {
        id: id,
        name: 'Chat',
        avatar: '',
        isGroup: false,
        isOnline: false,
        participantIds: [currentUser.id],
      };

      setChatData(basicChatData);
      console.log('✅ Basic offline chat created');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.fullScreenContainer}>
        <StatusBar style="light" backgroundColor={IRACHAT_COLORS.primary} />
        <ResponsiveContainer centered wallpaperVariant="chat">
          <ActivityIndicator size="large" color={IRACHAT_COLORS.primary} />
          <Text style={styles.loadingText}>Loading chat...</Text>
        </ResponsiveContainer>
      </View>
    );
  }

  if (error || !chatData) {
    return (
      <View style={styles.fullScreenContainer}>
        <StatusBar style="light" backgroundColor={IRACHAT_COLORS.primary} />
        <ResponsiveContainer centered wallpaperVariant="chat">
          <View style={styles.errorContainer}>
            <Ionicons
              name="warning-outline"
              size={48}
              color={IRACHAT_COLORS.error}
              style={styles.errorIcon}
            />
            <Text style={styles.errorText}>
              {error || 'Failed to load chat data'}
            </Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={loadChatData}
              activeOpacity={0.7}
            >
              <Ionicons name="refresh" size={20} color={IRACHAT_COLORS.textOnPrimary} />
              <Text style={styles.retryButtonText}>Try Again</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.goBackButton}
              onPress={() => router.back()}
              activeOpacity={0.7}
            >
              <Ionicons name="arrow-back" size={20} color={IRACHAT_COLORS.primary} />
              <Text style={styles.goBackButtonText}>Go Back</Text>
            </TouchableOpacity>
          </View>
        </ResponsiveContainer>
      </View>
    );
  }

  // Determine if current user is admin (for group chats)
  const isAdmin = chatData.isGroup && (
    currentUser?.id === chatData.participantIds[0] || // First participant is often the creator
    currentUser?.username?.includes('admin') || // Simple admin check
    false
  );

  // Render appropriate ULTIMATE chat component for best user experience
  if (chatData.isGroup) {
    return (
      <UltimateGroupChatRoom
        groupId={chatData.id}
        groupName={chatData.name}
        groupAvatar={chatData.avatar}
        isAdmin={isAdmin}
        currentUserId={currentUser?.id || ''}
        currentUserName={currentUser?.displayName || currentUser?.username || 'User'}
        currentUserAvatar={currentUser?.photoURL || ''}
      />
    );
  } else {
    // Get the other participant's ID for individual chats
    const partnerId = chatData.participantIds.find(id => id !== currentUser?.id) || chatData.participantIds[0] || 'unknown';

    return (
      <UltimateIndividualChatRoom
        chatId={chatData.id}
        partnerName={chatData.name}
        partnerAvatar={chatData.avatar || ''}
        isOnline={chatData.isOnline || false}
        partnerId={partnerId}
        currentUser={currentUser || undefined}
        currentUserId={currentUser?.id || ''}
      />
    );
  }
}

const styles = StyleSheet.create({
  fullScreenContainer: {
    flex: 1,
    backgroundColor: IRACHAT_COLORS.background,
  },
  loadingText: {
    marginTop: ResponsiveSpacing.md,
    fontSize: ResponsiveTypography.fontSize.base,
    color: IRACHAT_COLORS.textSecondary,
    fontFamily: TYPOGRAPHY.fontFamily,
    textAlign: 'center',
  },
  errorContainer: {
    alignItems: 'center',
    paddingHorizontal: ResponsiveSpacing.lg,
  },
  errorIcon: {
    marginBottom: ResponsiveSpacing.md,
  },
  errorText: {
    fontSize: ResponsiveTypography.fontSize.lg,
    color: IRACHAT_COLORS.error,
    fontFamily: TYPOGRAPHY.fontFamily,
    textAlign: 'center',
    lineHeight: ResponsiveTypography.fontSize.lg * 1.5,
    marginBottom: ResponsiveSpacing.lg,
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: IRACHAT_COLORS.primary,
    paddingHorizontal: ResponsiveSpacing.lg,
    paddingVertical: ResponsiveSpacing.md,
    borderRadius: 8,
    marginBottom: ResponsiveSpacing.md,
  },
  retryButtonText: {
    color: IRACHAT_COLORS.textOnPrimary,
    fontSize: ResponsiveTypography.fontSize.base,
    fontFamily: TYPOGRAPHY.fontFamily,
    fontWeight: '600',
    marginLeft: ResponsiveSpacing.sm,
  },
  goBackButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'transparent',
    paddingHorizontal: ResponsiveSpacing.lg,
    paddingVertical: ResponsiveSpacing.md,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: IRACHAT_COLORS.primary,
  },
  goBackButtonText: {
    color: IRACHAT_COLORS.primary,
    fontSize: ResponsiveTypography.fontSize.base,
    fontFamily: TYPOGRAPHY.fontFamily,
    fontWeight: '600',
    marginLeft: ResponsiveSpacing.sm,
  },
});
