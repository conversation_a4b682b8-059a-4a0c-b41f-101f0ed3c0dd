// 📱 REAL UPDATES TAB - Fully functional social media
// Real camera capture, media upload, story posting, and social interactions

import { Ionicons } from "@expo/vector-icons";
import * as ImagePicker from 'expo-image-picker';
import * as MediaLibrary from 'expo-media-library';
import * as FileSystem from 'expo-file-system';
import { useRouter } from "expo-router";
import React, { useCallback, useEffect, useState, useRef } from "react";
import { AppState } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import {
  ActivityIndicator,
  Alert,
  Animated,
  Dimensions,
  FlatList,
  Image,
  KeyboardAvoidingView,
  Modal,
  PanResponder,
  Platform,
  RefreshControl,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,

} from "react-native";
// Removed gesture handler imports - pure video feed only
import { VideoView, useVideoPlayer } from 'expo-video';
import { useSelector } from "react-redux";
import { RootState } from "../../src/redux/store";
import { realUpdatesService, RealUpdate, UpdateType } from "../../src/services/realUpdatesService";
import { localUpdatesStorage } from "../../src/services/localUpdatesStorage";
import { updatesSyncService } from "../../src/services/updatesSyncService";
import { Update } from "../../src/types/Update";
import { formatTimeAgo } from "../../src/utils/dateUtils";
import { UpdatesInteractionPages } from "../../src/components/UpdatesInteractionPages";
import { UpdatesCommentsPage } from "../../src/components/UpdatesCommentsPage";
import NetInfo from '@react-native-community/netinfo';
import { DeviceInfo } from "../../src/utils/responsiveUtils";
import { auth } from "../../src/services/firebaseSimple";
import { VideoTrimmer } from "../../src/components/media/VideoTrimmer";
import { PhotoCropper } from "../../src/components/media/PhotoCropper";
import { TikTokStyleMediaPicker } from "../../src/components/media/TikTokStyleMediaPicker";
import { TikTokStyleCamera } from "../../src/components/media/TikTokStyleCamera";
import { MediaPreviewEditor } from "../../src/components/media/MediaPreviewEditor";
import { TextUpdateCreator } from "../../src/components/media/TextUpdateCreator";
import { AudioCaption, TextOverlay } from "../../src/types/Update";
import { AudioCaptionPlayer } from "../../src/components/media/AudioCaptionPlayer";
import { TextOverlayRenderer } from "../../src/components/media/TextOverlayRenderer";
import { audioCaptionService } from "../../src/services/audioCaptionService";

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// GLOBAL VIDEO PLAYER MANAGER - Singleton pattern to prevent re-initialization
class VideoPlayerManager {
  private static instance: VideoPlayerManager;
  private players = new Map<string, any>();
  private playerRefs = new Map<string, React.RefObject<any>>();
  private initializingPlayers = new Set<string>();

  static getInstance(): VideoPlayerManager {
    if (!VideoPlayerManager.instance) {
      VideoPlayerManager.instance = new VideoPlayerManager();
    }
    return VideoPlayerManager.instance;
  }

  getPlayer(videoId: string): any | null {
    return this.players.get(videoId) || null;
  }

  hasPlayer(videoId: string): boolean {
    return this.players.has(videoId);
  }

  isInitializing(videoId: string): boolean {
    return this.initializingPlayers.has(videoId);
  }

  setPlayer(videoId: string, player: any): void {
    console.log('🎯 VideoPlayerManager: Setting player for', videoId);
    this.players.set(videoId, player);
    this.initializingPlayers.delete(videoId);
  }

  markAsInitializing(videoId: string): void {
    this.initializingPlayers.add(videoId);
  }

  removePlayer(videoId: string): void {
    console.log('🗑️ VideoPlayerManager: Removing player for', videoId);
    const player = this.players.get(videoId);
    if (player) {
      try {
        player.pause();
      } catch (error) {
        // Player might already be released
      }
    }
    this.players.delete(videoId);
    this.playerRefs.delete(videoId);
    this.initializingPlayers.delete(videoId);
  }

  pauseAllPlayers(): void {
    this.players.forEach((player, videoId) => {
      try {
        if (player && typeof player.pause === 'function') {
          player.pause();
        }
      } catch (error) {
        console.warn('Error pausing player:', videoId, error);
        this.removePlayer(videoId);
      }
    });
  }

  getAllPlayers(): Map<string, any> {
    return this.players;
  }
}

// Global instance
const videoPlayerManager = VideoPlayerManager.getInstance();

// Global function to safely pause all active video players
const pauseAllActiveVideos = () => {
  videoPlayerManager.pauseAllPlayers();
};

// Convert RealUpdate to Update format for local storage
const convertRealUpdateToUpdate = (realUpdate: RealUpdate): Update => {
  return {
    id: realUpdate.id,
    userId: realUpdate.userId,
    userName: realUpdate.userName,
    userAvatar: realUpdate.userAvatar,
    type: realUpdate.type === 'photo' ? 'image' : realUpdate.type === 'video' ? 'video' : 'text',
    caption: realUpdate.caption || realUpdate.content,
    timestamp: realUpdate.timestamp,
    isStory: !!realUpdate.expiresAt,
    expiresAt: realUpdate.expiresAt,
    privacy: realUpdate.privacy,
    isVisible: true,
    isArchived: false,
    location: undefined, // Location feature disabled
    hashtags: [],
    mentions: [],
    groupTags: [],
    media: realUpdate.mediaUrl ? [{
      id: `${realUpdate.id}_media`,
      url: realUpdate.mediaUrl,
      type: realUpdate.type === 'photo' ? 'image' : 'video',
      width: realUpdate.mediaWidth,
      height: realUpdate.mediaHeight,
      duration: realUpdate.videoDuration,
    }] : [],
    likes: realUpdate.likes, // Array of user IDs
    views: realUpdate.views.map(userId => ({
      userId,
      userName: 'Unknown',
      timestamp: new Date(),
      type: 'view' as const
    })),
    shares: realUpdate.shares.map(userId => ({
      userId,
      userName: 'Unknown',
      timestamp: new Date(),
      type: 'share' as const
    })),
    downloads: [],
    reactions: [],
    comments: [],
    viewCount: realUpdate.views.length,
    likeCount: realUpdate.likes.length,
    commentCount: realUpdate.comments.length,
    shareCount: realUpdate.shares.length,
    downloadCount: 0,
    isLikedByCurrentUser: false,
    isViewedByCurrentUser: false,
    isSharedByCurrentUser: false,
    isDownloadedByCurrentUser: false,
    isReported: false,
    reportCount: 0,
    isFlagged: false,
    isPinned: false,
    isHighlight: false,
    musicTrack: undefined,
  };
};

const UpdatesScreen: React.FC = () => {
  const router = useRouter();
  const currentUser = useSelector((state: RootState) => state.user.currentUser);

  const [updates, setUpdates] = useState<RealUpdate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showCameraOptions, setShowCameraOptions] = useState(false);
  const [showVideoTrimmer, setShowVideoTrimmer] = useState(false);
  const [showPhotoCropper, setShowPhotoCropper] = useState(false);

  // Interaction pages state
  const [showInteractionPages, setShowInteractionPages] = useState(false);
  const [selectedUpdateId, setSelectedUpdateId] = useState<string>('');
  const [selectedInteractionTab, setSelectedInteractionTab] = useState<'likes' | 'views' | 'shares' | 'downloads'>('likes');

  // Removed story management state - pure video feed only

  // Search State
  const [showSearch, setShowSearch] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<RealUpdate[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isOnline, setIsOnline] = useState(true);

  // Video playback control
  const [currentlyPlayingVideo, setCurrentlyPlayingVideo] = useState<string | null>(null);
  const [globalVideoPause, setGlobalVideoPause] = useState(false); // Global pause for all videos
  const flatListRef = useRef<FlatList>(null);



  // Removed tab system - pure TikTok-style video feed only

  // Comments page state
  const [showCommentsPage, setShowCommentsPage] = useState(false);
  const [commentsUpdateId, setCommentsUpdateId] = useState<string>('');

  // Caption input modal state
  const [showCaptionModal, setShowCaptionModal] = useState(false);
  const [captionText, setCaptionText] = useState('');
  const [captionVideoPlaying, setCaptionVideoPlaying] = useState(true);
  const [captionModalData, setCaptionModalData] = useState<{
    mediaUri: string;
    type: 'photo' | 'video';
    isStory: boolean;
  } | null>(null);

  // Download progress state for real-time tracking
  const [downloadingUpdates, setDownloadingUpdates] = useState<Set<string>>(new Set());
  const [downloadProgress, setDownloadProgress] = useState<{ [key: string]: number }>({});

  // TikTok-style media creation states
  const [showTikTokMediaPicker, setShowTikTokMediaPicker] = useState(false);
  const [showTikTokCamera, setShowTikTokCamera] = useState(false);
  const [showMediaPreviewEditor, setShowMediaPreviewEditor] = useState(false);
  const [showTextUpdateCreator, setShowTextUpdateCreator] = useState(false);
  const [selectedMediaForPreview, setSelectedMediaForPreview] = useState<{
    uri: string;
    type: 'photo' | 'video';
  } | null>(null);
  const [isPostingUpdate, setIsPostingUpdate] = useState(false);

  // Network monitoring
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsOnline(state.isConnected ?? false);
    });

    return unsubscribe;
  }, []);

  // Initialize local storage and load updates on component mount
  useEffect(() => {
    const initializeAndLoad = async () => {
      try {
        // Initialize local storage
        await localUpdatesStorage.initialize();

        // Initialize audio caption service
        await audioCaptionService.initialize();

        // Clear expired stories
        await localUpdatesStorage.clearExpiredStories();

        // Start background sync service
        try {
          if (updatesSyncService && typeof updatesSyncService.startBackgroundSync === 'function') {
            updatesSyncService.startBackgroundSync();
          }
        } catch (error) {
          console.error('Error starting background sync:', error);
        }
      } catch (error) {
        console.error('❌ Failed to initialize local storage:', error);
      }
    };

    if (currentUser?.id) {
      // Check Firebase authentication status

      // Check if Firebase user matches Redux user
      if (auth?.currentUser?.uid !== currentUser?.id) {


        // If using temporary user, switch to local-only mode
        if (currentUser?.id?.startsWith('temp_')) {
          setIsOnline(false); // Force offline mode for temp users
        }
      }

      initializeAndLoad();
      loadUpdates();

      // Initialize and start background sync for offline updates
      updatesSyncService.startBackgroundSync();

      // Subscribe to real-time updates with error handling
      let unsubscribe: (() => void) | null = null;

      try {
        unsubscribe = realUpdatesService.subscribeToUpdatesFeed(
          currentUser.id,
          (newUpdates) => {
            setUpdates(newUpdates);
            setIsLoading(false);

            // Save real-time updates to local storage
            newUpdates.forEach(async (update) => {
              try {
                const convertedUpdate = convertRealUpdateToUpdate(update);
                await localUpdatesStorage.saveUpdate(convertedUpdate);
              } catch (error) {
                console.error('⚠️ Failed to save real-time update locally:', error);
              }
            });
          }
        );
      } catch (firebaseError) {
        console.error('🔥 Firebase subscription failed, using local-only mode:', firebaseError);
        // Continue with local-only mode - updates will still work offline
        setIsLoading(false);
      }

      return () => {
        if (unsubscribe) {
          unsubscribe();
        }
        updatesSyncService.stopBackgroundSync();
        // Stop all video playback on unmount
        setCurrentlyPlayingVideo(null);
      };
    }
  }, [currentUser?.id]);

  // CRITICAL: App State and Navigation Lifecycle Management
  useEffect(() => {
    // App state change listener (background/foreground)
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'background' || nextAppState === 'inactive') {
        pauseAllActiveVideos(); // Immediately pause all videos
        setGlobalVideoPause(true);
        setCurrentlyPlayingVideo(null);
      } else if (nextAppState === 'active') {
        setGlobalVideoPause(false);
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, []);

  // Page focus/blur management with immediate video playback
  useFocusEffect(
    useCallback(() => {
      // Page focused - enable videos and start playing first video immediately
      setGlobalVideoPause(false);

      // Start playing the first video after a delay to ensure proper mounting
      const timeoutId = setTimeout(() => {
        if (updates.length > 0 && !globalVideoPause) {
          const firstVideo = updates.find(update => update.type === 'video');
          if (firstVideo && videoPlayerManager.hasPlayer(firstVideo.id)) {
            setCurrentlyPlayingVideo(firstVideo.id);
          }
        }
      }, 150);

      return () => {
        clearTimeout(timeoutId);
        // CRITICAL: Stop ALL videos when leaving updates tab
        pauseAllActiveVideos(); // Immediately pause all videos
        setGlobalVideoPause(true);
        setCurrentlyPlayingVideo(null);
      };
    }, [updates, currentlyPlayingVideo])
  );

  // Modal state management - pause videos when modals/pages open
  useEffect(() => {
    const anyModalOpen = showCaptionModal || showCameraOptions || showCreateModal || showSearch ||
                        showInteractionPages || showCommentsPage || showVideoTrimmer || showPhotoCropper ||
                        showTikTokMediaPicker || showTikTokCamera || showMediaPreviewEditor || showTextUpdateCreator;

    if (anyModalOpen) {
      pauseAllActiveVideos(); // Immediately pause all videos
      setGlobalVideoPause(true);
      setCurrentlyPlayingVideo(null);
      // Also pause all audio captions when modals are open
      audioCaptionService.setGlobalAudioPause(true);
    } else {
      setGlobalVideoPause(false);
      // Resume audio captions when modals are closed
      audioCaptionService.setGlobalAudioPause(false);
    }
  }, [showCaptionModal, showCameraOptions, showCreateModal, showSearch, showInteractionPages, showCommentsPage, showVideoTrimmer, showPhotoCropper, showTikTokMediaPicker, showTikTokCamera, showMediaPreviewEditor, showTextUpdateCreator]);

  // Delete update function
  const handleDeleteUpdate = async (updateId: string, userId: string) => {
    // Check if current user is the owner
    if (currentUser?.id !== userId) {
      Alert.alert('Error', 'You can only delete your own updates');
      return;
    }

    Alert.alert(
      'Delete Update',
      'Are you sure you want to delete this update? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              // Remove from UI immediately for better UX
              setUpdates(prevUpdates => prevUpdates.filter(u => u.id !== updateId));

              // Remove from local storage (use updateId as localId for local updates)
              try {
                await localUpdatesStorage.deleteUpdate(updateId);
              } catch (localError) {
                // Local delete failed, continuing with Firebase delete
              }

              // Try to delete from Firebase if online and not a temp user
              if (isOnline && !userId.startsWith('temp_')) {
                try {
                  // Note: realUpdatesService.deleteUpdate might need to be implemented
                  // For now, we'll just remove it locally
                  //Firebase delete would be called here
                } catch (error) {
                  // Failed to delete from Firebase, but removed locally
                }
              }

              Alert.alert('Success', 'Update deleted successfully');
            } catch (error) {
              console.error('Failed to delete update:', error);
              Alert.alert('Error', 'Failed to delete update');
              // Re-add the update to UI if deletion failed
              loadUpdates();
            }
          },
        },
      ]
    );
  };

  // Viewport detection for video playback
  const lastViewabilityChange = useRef<number>(0);

  // Preloading state for smooth transitions (implementation in progress)

  const onViewableItemsChanged = useCallback(({ viewableItems }: any) => {
    const now = Date.now();
    // Throttle viewability changes to prevent rapid state updates
    if (now - lastViewabilityChange.current < 100) {
      return;
    }
    lastViewabilityChange.current = now;

    const visibleItems = viewableItems.map((item: any) => item.item);
    const visibleVideoIds = visibleItems
      .filter((item: any) => item.type === 'video')
      .map((item: any) => item.id);

    // CRITICAL: If there's a photo OR text post in viewport, stop ALL videos immediately
    // This ensures TikTok-style behavior where only videos play, and they pause for any non-video content
    const hasNonVideoInViewport = visibleItems.some((item: any) =>
      item.type === 'photo' || item.type === 'text'
    );

    if (hasNonVideoInViewport) {
      setCurrentlyPlayingVideo(null);
      // Also pause all audio captions when videos are paused
      audioCaptionService.pauseAllAudios();
      return; // Don't start any videos when non-video content is visible
    }

    // Immediately stop any video that's currently playing but not in viewport
    if (currentlyPlayingVideo && !visibleVideoIds.includes(currentlyPlayingVideo)) {
      setCurrentlyPlayingVideo(null);
    }

    // Start playing the first visible video ONLY if no non-video content is visible
    if (visibleVideoIds.length > 0 && !currentlyPlayingVideo && !hasNonVideoInViewport) {
      const videoToPlay = visibleVideoIds[0];

      if (videoPlayerManager.hasPlayer(videoToPlay)) {
        console.log('📺 Playing first visible video:', videoToPlay);
        setCurrentlyPlayingVideo(videoToPlay);
      }
    }
  }, [currentlyPlayingVideo, updates]);

  // REMOVED: Redundant effect that was causing infinite play/pause cycles
  // Video playback is now handled entirely by individual VideoPlayer components

  const viewabilityConfig = {
    itemVisiblePercentThreshold: 30, // Even more responsive
    minimumViewTime: 50, // Immediate response
    waitForInteraction: false,
  };

  // Video Progress Bar Component
  const VideoProgressBar = ({
    player,
    videoId,
    isVisible
  }: {
    player: any;
    videoId: string;
    isVisible: boolean;
  }) => {
    const [progress, setProgress] = useState(0);
    const [duration, setDuration] = useState(0);
    const [isDragging, setIsDragging] = useState(false);
    const progressAnim = useRef(new Animated.Value(0)).current;
    const panResponder = useRef<any>(null);

    // Update progress from video player
    useEffect(() => {
      if (!player || !isVisible) return;

      const interval = setInterval(() => {
        try {
          if (player.currentTime !== undefined && player.duration !== undefined) {
            const currentProgress = player.duration > 0 ? player.currentTime / player.duration : 0;
            if (!isDragging) {
              setProgress(currentProgress);
              Animated.timing(progressAnim, {
                toValue: currentProgress,
                duration: 100,
                useNativeDriver: false,
              }).start();
            }
            if (duration !== player.duration) {
              setDuration(player.duration);
            }
          }
        } catch (error) {
          // Player might not be ready
        }
      }, 100);

      return () => clearInterval(interval);
    }, [player, isVisible, isDragging, duration]);

    // Create pan responder for touch interactions
    useEffect(() => {
      panResponder.current = PanResponder.create({
        onStartShouldSetPanResponder: () => true,
        onMoveShouldSetPanResponder: () => true,

        onPanResponderGrant: (evt) => {
          setIsDragging(true);
          const { locationX } = evt.nativeEvent;
          // Account for container padding and right margin
          const containerWidth = SCREEN_WIDTH - 80 - 32; // 80 for right actions, 32 for padding (16*2)
          const adjustedLocationX = locationX - 16; // Account for left padding
          const newProgress = Math.max(0, Math.min(1, adjustedLocationX / containerWidth));
          setProgress(newProgress);
          progressAnim.setValue(newProgress);

          // Immediate seek on tap
          if (player && duration > 0) {
            try {
              const seekTime = newProgress * duration;
              player.currentTime = seekTime;
            } catch (error) {
              console.error('Error seeking video:', error);
            }
          }
        },

        onPanResponderMove: (evt) => {
          const { locationX } = evt.nativeEvent;
          // Account for container padding and right margin
          const containerWidth = SCREEN_WIDTH - 80 - 32; // 80 for right actions, 32 for padding (16*2)
          const adjustedLocationX = locationX - 16; // Account for left padding
          const newProgress = Math.max(0, Math.min(1, adjustedLocationX / containerWidth));
          setProgress(newProgress);
          progressAnim.setValue(newProgress);

          // Real-time seeking while dragging
          if (player && duration > 0) {
            try {
              const seekTime = newProgress * duration;
              player.currentTime = seekTime;
            } catch (error) {
              console.error('Error seeking video:', error);
            }
          }
        },

        onPanResponderRelease: () => {
          // Small delay before allowing automatic progress updates again
          setTimeout(() => {
            setIsDragging(false);
          }, 100);
        },
      });
    }, [duration, player]);

    if (!isVisible) return null;

    return (
      <View style={styles.videoProgressContainer} {...panResponder.current?.panHandlers}>
        <View style={styles.videoProgressTrack}>
          <Animated.View
            style={[
              styles.videoProgressFill,
              {
                width: progressAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0%', '100%'],
                  extrapolate: 'clamp',
                }),
              }
            ]}
          />
        </View>
      </View>
    );
  };

  // Video Player Component with singleton pattern to prevent re-initialization
  const VideoPlayer = React.memo(({
    source,
    style,
    isLooping = true,
    videoId,
    isInViewport = false
  }: {
    source: { uri: string };
    style: any;
    isLooping?: boolean;
    videoId: string;
    isInViewport?: boolean;
  }) => {
    const [isMuted, setIsMuted] = useState(false);
    const lastTap = useRef<number>(0);
    const lastPlayerAction = useRef<number>(0);
    const componentMountedRef = useRef(true);

    // Get existing player or create new one ONLY if it doesn't exist
    const existingPlayer = videoPlayerManager.getPlayer(videoId);
    const shouldCreatePlayer = !existingPlayer && !videoPlayerManager.isInitializing(videoId);

    // Only call useVideoPlayer if we need to create a new player
    const newPlayer = useVideoPlayer(
      shouldCreatePlayer ? source.uri : '', // Empty URI if we don't need to create
      shouldCreatePlayer ? (player) => {
        try {
          console.log('🎬 Initializing video player:', { videoId, uri: source.uri });

          player.loop = isLooping;
          player.muted = false;

          // Register with manager
          videoPlayerManager.setPlayer(videoId, player);

          console.log('✅ Video player initialized successfully');
        } catch (error) {
          console.error('❌ Video player initialization error:', error);
        }
      } : undefined
    );

    // Get the actual player to use (existing or newly created)
    const player = existingPlayer || newPlayer;

    // Mark as initializing when we start creating a new player
    useEffect(() => {
      if (shouldCreatePlayer && !videoPlayerManager.isInitializing(videoId)) {
        videoPlayerManager.markAsInitializing(videoId);
      }
    }, [shouldCreatePlayer, videoId]);

    // Safe player control functions using the manager
    const safePlayerPlay = useCallback(() => {
      if (!player || !componentMountedRef.current) return false;

      // Verify player is still registered with manager
      const managerPlayer = videoPlayerManager.getPlayer(videoId);
      if (!managerPlayer || managerPlayer !== player) return false;

      try {
        player.play();
        return true;
      } catch (error) {
        console.error('Error playing video:', error);
        videoPlayerManager.removePlayer(videoId);
        return false;
      }
    }, [player, videoId]);

    const safePlayerPause = useCallback(() => {
      if (!player || !componentMountedRef.current) return false;

      try {
        player.pause();
        return true;
      } catch (error) {
        console.error('Error pausing video:', error);
        videoPlayerManager.removePlayer(videoId);
        return false;
      }
    }, [player, videoId]);

    // Simplified playback control using the manager
    useEffect(() => {
      if (!player || !componentMountedRef.current) return;

      // Verify player is still managed
      const managerPlayer = videoPlayerManager.getPlayer(videoId);
      if (!managerPlayer || managerPlayer !== player) return;

      // Debounce player actions
      const now = Date.now();
      if (now - lastPlayerAction.current < 300) return; // Increased debounce
      lastPlayerAction.current = now;

      // Determine if video should play
      const shouldPlay = !globalVideoPause &&
                        currentlyPlayingVideo === videoId &&
                        isInViewport;

      if (shouldPlay) {
        player.muted = isMuted;
        safePlayerPlay();
      } else {
        safePlayerPause();
      }
    }, [isInViewport, globalVideoPause, currentlyPlayingVideo, player, videoId, isMuted, safePlayerPlay, safePlayerPause]);

    // Cleanup on unmount - CRITICAL: Only remove from manager if this component created the player
    useEffect(() => {
      return () => {
        componentMountedRef.current = false;

        // Only remove if this component created the player (not if it was reused)
        if (shouldCreatePlayer && player) {
          console.log('🗑️ Component unmounting, removing player:', videoId);
          videoPlayerManager.removePlayer(videoId);
        }
      };
    }, [videoId, shouldCreatePlayer, player]);

    // Update mute state
    useEffect(() => {
      if (!player || !componentMountedRef.current) return;

      try {
        player.muted = isMuted;
      } catch (error) {
        console.error('Error setting mute state:', error);
      }
    }, [isMuted, player]);

    // Handle tap controls - Double tap for audio mute only
    const handleVideoTap = () => {
      const now = Date.now();
      const DOUBLE_TAP_DELAY = 300;

      if (now - lastTap.current < DOUBLE_TAP_DELAY) {
        // Double tap - toggle audio mute only (video continues playing)
        setIsMuted(!isMuted);
        lastTap.current = 0; // Reset to prevent triple tap issues
      }

      lastTap.current = now;
    };

    return (
      <View style={style}>
        <VideoView
          key={`video-${videoId}-${source.uri}`}
          style={style}
          player={player}
          allowsFullscreen={false}
          allowsPictureInPicture={false}
          contentFit="cover"
          nativeControls={false}
        />

        {/* Invisible tap overlay for better touch detection */}
        <TouchableOpacity
          style={styles.videoTapOverlay}
          onPress={handleVideoTap}
          activeOpacity={1}
        />

        {/* Removed pause overlay - using FAB instead */}

        {/* Visual feedback for mute state */}
        {isMuted && isInViewport && (
          <View style={styles.videoMuteIndicator}>
            <Ionicons name="volume-mute" size={24} color="rgba(255,255,255,0.8)" />
          </View>
        )}




      </View>
    );
  });

  // Load updates (Local-first with Firebase sync)
  const loadUpdates = useCallback(async () => {
    if (!currentUser?.id) return;

    try {
      setIsLoading(true);

      // 🏠 LOAD FROM LOCAL STORAGE FIRST
      const localUpdates = await localUpdatesStorage.getUpdates(50, 0, false);

      // Convert Update[] to RealUpdate[] for display - ROBUST CONVERSION
      const convertedLocalUpdates: RealUpdate[] = localUpdates.map((update: Update) => ({
        id: update.id,
        userId: update.userId,
        userName: update.userName,
        userAvatar: update.userAvatar,
        content: update.caption || '',
        caption: update.caption,
        type: update.type === 'image' ? 'photo' : update.type === 'video' ? 'video' : 'text',
        mediaUrl: update.media && update.media.length > 0 ? update.media[0].url : undefined,
        mediaWidth: update.media && update.media.length > 0 ? update.media[0].width : undefined,
        mediaHeight: update.media && update.media.length > 0 ? update.media[0].height : undefined,
        videoDuration: update.media && update.media.length > 0 ? update.media[0].duration : undefined,
        privacy: update.privacy as 'public' | 'friends' | 'private',
        timestamp: update.timestamp,
        expiresAt: update.expiresAt,
        likes: update.likes || [],
        comments: [], // Will be loaded separately if needed
        shares: (update.shares || []).map((s: any) => s.userId || s),
        views: (update.views || []).map((v: any) => v.userId || v),
        downloads: (update.downloads || []).map((d: any) => d.userId || d),
        location: undefined, // Location feature disabled
        tags: update.hashtags || [],
        mentions: update.mentions || [],
        musicTrack: update.musicTrack ? {
          title: update.musicTrack.title,
          artist: update.musicTrack.artist,
          url: update.musicTrack.localUri || ''
        } : undefined,
        musicArtist: update.musicTrack?.artist,
      }));



      // Set local updates immediately for offline functionality
      setUpdates(convertedLocalUpdates);

      // Auto-play first video when updates are loaded (if not globally paused)
      if (convertedLocalUpdates.length > 0 && !globalVideoPause) {
        const firstVideo = convertedLocalUpdates.find(update => update.type === 'video');
        if (firstVideo) {
          // Add a small delay to ensure video players are properly mounted
          setTimeout(() => {
            // Double-check that the video player is still registered
            if (videoPlayerManager.hasPlayer(firstVideo.id)) {
              setCurrentlyPlayingVideo(firstVideo.id);
            }
          }, 200);
        }
      }

      // 🔄 SYNC WITH FIREBASE IN BACKGROUND (Skip for temporary users)
      if (!currentUser.id.startsWith('temp_') && isOnline) {
        try {
          const feedUpdates = await realUpdatesService.getUpdatesFeed(currentUser.id);

          // Save Firebase updates to local storage
          for (const update of feedUpdates) {
            try {
              const convertedUpdate = convertRealUpdateToUpdate(update);
              await localUpdatesStorage.saveUpdate(convertedUpdate);
            } catch (error) {
              // Failed to save Firebase update locally
            }
          }

          // Merge Firebase data with local data (don't replace completely)
          setUpdates(prevUpdates => {
            const firebaseIds = new Set(feedUpdates.map(u => u.id));
            const localOnlyUpdates = prevUpdates.filter(u => !firebaseIds.has(u.id));
            const mergedUpdates = [...feedUpdates, ...localOnlyUpdates];

            // Simple auto-play first video after Firebase sync
            if (mergedUpdates.length > 0 && !currentlyPlayingVideo && !globalVideoPause) {
              const firstVideo = mergedUpdates.find(update => update.type === 'video');
              if (firstVideo && videoPlayerManager.hasPlayer(firstVideo.id)) {
                console.log('🎯 Auto-playing first video after sync:', firstVideo.id);
                setCurrentlyPlayingVideo(firstVideo.id);
              }
            }

            return mergedUpdates;
          });
        } catch (firebaseError: any) {
          console.error('🔥 Firebase sync failed:', firebaseError?.message || firebaseError);

          // Check if it's a permission error
          if (firebaseError?.code === 'permission-denied') {

            // Keep using local data - don't show error to user
          } else {

          }

          // Continue with local data only - this is expected behavior
        }
      } else {

      }

    } catch (error) {
      console.error('❌ Error loading updates:', error);
      // Fallback to empty array if both local and Firebase fail
      setUpdates([]);
    } finally {
      setIsLoading(false);
    }
  }, [currentUser?.id]);

  // Refresh updates
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    await loadUpdates();
    setIsRefreshing(false);
  }, [loadUpdates]);

  // Handle like toggle
  const handleLike = async (updateId: string) => {
    if (!currentUser?.id) return;
    
    try {
      const result = await realUpdatesService.toggleLike(updateId, currentUser.id);
      if (result.success) {
        // Update local state
        setUpdates(prev => prev.map(update => {
          if (update.id === updateId) {
            const newLikes = result.isLiked 
              ? [...update.likes, currentUser.id]
              : update.likes.filter(id => id !== currentUser.id);
            return { ...update, likes: newLikes };
          }
          return update;
        }));
      }
    } catch (error) {
      console.error('❌ Error toggling like:', error);
    }
  };

  // Handle comment
  const handleComment = async (updateId: string) => {
    try {
      setCommentsUpdateId(updateId);
      setShowCommentsPage(true);
    } catch (error) {
      console.error('❌ Error opening comments:', error);
    }
  };

  // Handle share
  const handleShare = async (updateId: string) => {
    if (!currentUser?.id) return;
    
    try {
      const result = await realUpdatesService.shareUpdate(updateId, currentUser.id);
      if (result.success) {
        Alert.alert('Shared!', 'Update shared successfully');
        
        // Update local state
        setUpdates(prev => prev.map(update => {
          if (update.id === updateId) {
            return { ...update, shares: [...update.shares, currentUser.id] };
          }
          return update;
        }));
      } else {
        Alert.alert('Error', result.error || 'Failed to share update');
      }
    } catch (error) {
      console.error('❌ Error sharing update:', error);
      Alert.alert('Error', 'Failed to share update');
    }
  };

  // Handle download with real Firebase functionality and progress tracking
  const handleDownload = async (updateId: string) => {
    if (!currentUser?.id) return;

    try {
      const update = updates.find(u => u.id === updateId);
      if (!update?.mediaUrl) {
        Alert.alert('Error', 'No media to download');
        return;
      }

      // Check if already downloading
      if (downloadingUpdates.has(updateId)) {
        Alert.alert('Info', 'Download already in progress');
        return;
      }

      // Request media library permissions
      const { status } = await MediaLibrary.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Please grant media library permission to save media to your device.',
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Settings',
              onPress: () => MediaLibrary.requestPermissionsAsync(),
            },
          ]
        );
        return;
      }

      // Start download tracking
      setDownloadingUpdates(prev => new Set(prev).add(updateId));
      setDownloadProgress(prev => ({ ...prev, [updateId]: 0 }));

      // Download media file from Firebase Storage with progress tracking
      const fileExtension = update.type === 'video' ? 'mp4' : 'jpg';
      const fileName = `IraChat_Update_${updateId}_${Date.now()}.${fileExtension}`;
      const fileUri = `${FileSystem.documentDirectory}${fileName}`;

      // Create download with progress callback
      const downloadResumable = FileSystem.createDownloadResumable(
        update.mediaUrl,
        fileUri,
        {},
        (downloadProgress) => {
          const progress = downloadProgress.totalBytesWritten / downloadProgress.totalBytesExpectedToWrite;
          setDownloadProgress(prev => ({ ...prev, [updateId]: Math.round(progress * 100) }));
        }
      );

      const downloadResult = await downloadResumable.downloadAsync();

      if (downloadResult && downloadResult.status === 200) {
        // Save to device media library
        await MediaLibrary.saveToLibraryAsync(downloadResult.uri);

        // Record download in Firebase with real-time tracking
        const result = await realUpdatesService.recordDownload(updateId, currentUser.id);

        if (result.success) {
          // Update local state immediately for real-time UI update
          setUpdates(prev => prev.map(u => {
            if (u.id === updateId) {
              return { ...u, downloads: [...u.downloads, currentUser.id] };
            }
            return u;
          }));

          Alert.alert('Success!', 'Media saved to your device gallery');
        } else {
          Alert.alert('Warning', 'Media saved but failed to record download. Please try again.');
        }
      } else {
        throw new Error('Download failed');
      }
    } catch (error) {
      console.error('❌ Error downloading media:', error);
      Alert.alert('Error', 'Failed to download media. Please check your internet connection and try again.');
    } finally {
      // Clean up download tracking
      setDownloadingUpdates(prev => {
        const newSet = new Set(prev);
        newSet.delete(updateId);
        return newSet;
      });
      setDownloadProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[updateId];
        return newProgress;
      });
    }
  };

  // Handle interaction page opening
  const openInteractionPage = (updateId: string, tab: 'likes' | 'views' | 'shares' | 'downloads') => {
    setSelectedUpdateId(updateId);
    setSelectedInteractionTab(tab);
    setShowInteractionPages(true);
  };

  // Handle user profile navigation
  const handleUserPress = (userId: string) => {
    // Navigate to user profile using router
    router.push(`/profile/${userId}`);
    setShowInteractionPages(false);
  };

  // Handle camera options - Updated for TikTok-style flow
  const handleCameraOption = (option: 'camera' | 'gallery' | 'video' | 'text') => {
    setShowCameraOptions(false);

    switch (option) {
      case 'camera':
        // Open TikTok-style camera
        setShowTikTokCamera(true);
        break;
      case 'gallery':
        // Open TikTok-style media picker
        setShowTikTokMediaPicker(true);
        break;
      case 'video':
        // Open TikTok-style camera (same as camera)
        setShowTikTokCamera(true);
        break;
      case 'text':
        // Open text update creator
        setShowTextUpdateCreator(true);
        break;
    }
  };

  // TikTok-style media handlers
  const handleTikTokMediaSelected = (uri: string, type: 'photo' | 'video') => {
    setSelectedMediaForPreview({ uri, type });
    setShowTikTokMediaPicker(false);
    setShowMediaPreviewEditor(true);
  };

  const handleTikTokCameraCapture = (uri: string, type: 'photo' | 'video') => {
    setSelectedMediaForPreview({ uri, type });
    setShowTikTokCamera(false);
    setShowMediaPreviewEditor(true);
  };

  const handleTikTokCameraPress = () => {
    setShowTikTokMediaPicker(false);
    setShowTikTokCamera(true);
  };

  const handleTikTokTextPress = () => {
    setShowTikTokMediaPicker(false);
    setShowTextUpdateCreator(true);
  };

  const handleMediaPreviewPost = async (caption: string, audioCaption?: AudioCaption, textOverlays?: TextOverlay[]) => {
    if (!selectedMediaForPreview || isPostingUpdate) return;

    setIsPostingUpdate(true);
    try {
      await createUpdate(selectedMediaForPreview.uri, selectedMediaForPreview.type, caption, audioCaption, textOverlays);

      // Close all modals and reset state
      setShowMediaPreviewEditor(false);
      setSelectedMediaForPreview(null);

      // Resume video playback after successful post
      setTimeout(() => {
        setGlobalVideoPause(false);
        // Auto-play the first video if available
        if (updates.length > 0) {
          const firstVideo = updates.find(update => update.type === 'video');
          if (firstVideo && videoPlayerManager.hasPlayer(firstVideo.id)) {
            setCurrentlyPlayingVideo(firstVideo.id);
          }
        }
      }, 300);

      // Show success message
      Alert.alert('Success!', 'Your update has been posted');
    } catch (error) {
      console.error('Error posting update:', error);
      Alert.alert('Error', 'Failed to post update. Please try again.');
    } finally {
      setIsPostingUpdate(false);
    }
  };

  const handleTextUpdatePost = async (text: string) => {
    if (!text.trim() || isPostingUpdate) return;

    setIsPostingUpdate(true);
    try {
      await createUpdate('', 'text', text);

      // Close modal
      setShowTextUpdateCreator(false);

      // Resume video playback after successful post
      setTimeout(() => {
        setGlobalVideoPause(false);
        // Auto-play the first video if available
        if (updates.length > 0) {
          const firstVideo = updates.find(update => update.type === 'video');
          if (firstVideo && videoPlayerManager.hasPlayer(firstVideo.id)) {
            setCurrentlyPlayingVideo(firstVideo.id);
          }
        }
      }, 300);

      // Show success message
      Alert.alert('Success!', 'Your text update has been posted');
    } catch (error) {
      console.error('Error posting text update:', error);
      Alert.alert('Error', 'Failed to post text update. Please try again.');
    } finally {
      setIsPostingUpdate(false);
    }
  };

  const handleCloseMediaPreview = () => {
    if (isPostingUpdate) return;
    setShowMediaPreviewEditor(false);
    setSelectedMediaForPreview(null);
  };

  const handleCloseTextCreator = () => {
    if (isPostingUpdate) return;
    setShowTextUpdateCreator(false);
  };

  // Open camera
  const openCamera = async (type: 'photo' | 'video') => {
    try {
      // CRITICAL: Pause all videos before opening camera

      setGlobalVideoPause(true);
      setCurrentlyPlayingVideo(null);

      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant camera permission to take photos/videos.');
        setGlobalVideoPause(false); // Resume videos if permission denied
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: type === 'photo' ? ['images'] : ['videos'],
        allowsEditing: false, // Disable automatic cropping
        quality: 1.0, // Full quality to preserve original dimensions
        videoMaxDuration: 600, // 10 minutes max for videos (user preference)
        exif: false, // Don't include EXIF data for privacy
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        // Detect actual media type from the asset
        const actualType = asset.type === 'video' ? 'video' : 'photo';

        showCreateUpdateModal(asset.uri, actualType);
        // Don't resume videos yet - modal is opening
      } else {
        // Camera cancelled - resume videos

        setGlobalVideoPause(false);
      }
    } catch (error) {
      console.error('❌ Error opening camera:', error);
      Alert.alert('Error', 'Failed to open camera');
      // Resume videos on error
      setGlobalVideoPause(false);
    }
  };

  // Open gallery
  const openGallery = async (type: 'photo' | 'video' | 'both') => {
    try {
      // CRITICAL: Pause all videos before opening gallery
      console.log('🖼️ Opening gallery - pausing all videos');
      setGlobalVideoPause(true);
      setCurrentlyPlayingVideo(null);

      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant photo library permission to select media.');
        setGlobalVideoPause(false); // Resume videos if permission denied
        return;
      }

      // Determine media types based on parameter (using new format)
      const mediaTypes = type === 'photo'
        ? 'images' as any
        : type === 'video'
        ? 'videos' as any
        : ['images', 'videos'] as any; // 'both' will show all media
      console.log('🖼️ Opening gallery with media types:', mediaTypes, 'for type:', type);

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: mediaTypes,
        allowsEditing: false, // Disable automatic cropping
        quality: 1.0, // Full quality to preserve original dimensions
        exif: false, // Don't include EXIF data for privacy
        videoQuality: 1.0, // Best video quality (0.0 to 1.0)
        videoMaxDuration: 600, // 10 minutes max for videos
        allowsMultipleSelection: false, // Single selection only
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        // Detect actual media type from the asset
        const actualType = asset.type === 'video' ? 'video' : 'photo';
        console.log('📱 Gallery asset type:', asset.type, 'Detected as:', actualType);
        showCreateUpdateModal(asset.uri, actualType);
        // Don't resume videos yet - modal is opening
      } else {
        // Gallery cancelled - resume videos
        console.log('🖼️ Gallery cancelled - resuming videos');
        setGlobalVideoPause(false);
      }
    } catch (error) {
      console.error('❌ Error opening gallery:', error);
      Alert.alert('Error', 'Failed to open gallery');
      // Resume videos on error
      setGlobalVideoPause(false);
    }
  };

  // Show create update modal with editing
  const showCreateUpdateModal = (mediaUri: string, type: 'photo' | 'video', isStory: boolean = false) => {
    // Immediately pause all videos when opening editing modal
    setGlobalVideoPause(true);
    setCurrentlyPlayingVideo(null);

    // Set up caption modal data for later use
    setCaptionModalData({ mediaUri, type, isStory });
    setCaptionText('');

    // Show appropriate editing modal
    if (type === 'video') {
      // Temporarily skip video trimmer to avoid conflicts
      setShowCaptionModal(true);
    } else {
      setShowPhotoCropper(true);
    }
  };

  // Handle video trim completion
  const handleVideoTrimComplete = (_trimmedUri: string, _startTime: number, _endTime: number) => {
    // TODO: Implement actual video trimming with expo-av
    // For now, just proceed to caption modal
    setShowVideoTrimmer(false);
    setShowCaptionModal(true);
  };

  // Handle photo crop completion
  const handlePhotoCropComplete = (croppedUri: string) => {
    // Update the media URI with cropped version
    if (captionModalData) {
      setCaptionModalData({ ...captionModalData, mediaUri: croppedUri });
    }
    setShowPhotoCropper(false);
    setShowCaptionModal(true);
  };

  // Handle editing cancellation
  const handleEditingCancel = () => {
    setShowVideoTrimmer(false);
    setShowPhotoCropper(false);
    setCaptionModalData(null);
    setCaptionText('');
    setTimeout(() => setGlobalVideoPause(false), 100);
  };

  // Handle caption submission
  const handleCaptionSubmit = async () => {
    if (!captionModalData) return;

    const { mediaUri, type } = captionModalData;

    // FIRST: Close modal and reset state to prevent UI issues
    setShowCaptionModal(false);
    setCaptionModalData(null);
    const captionToPost = captionText;
    setCaptionText('');

    // THEN: Create the update (this will add it to the feed)
    try {
      await createUpdate(mediaUri, type, captionToPost);

      // Resume videos after successful post
      setGlobalVideoPause(false);

      // Find and play the newly posted update after a delay
      setTimeout(() => {
        if (updates.length > 0) {
          const newestUpdate = updates[0];
          if (newestUpdate.type === 'video') {
            setCurrentlyPlayingVideo(newestUpdate.id);
          } else {
            // If newest update is a photo, stop all videos
            setCurrentlyPlayingVideo(null);
          }
        }
      }, 300); // Longer delay to ensure update list is refreshed
    } catch (error) {
      // If posting fails, still resume videos
      setGlobalVideoPause(false);
    }
  };

  // Create offline update
  const createOfflineUpdate = async (mediaUri: string, type: UpdateType, caption: string) => {
    if (!currentUser?.id) return { success: false, error: 'No user found' };

    try {
      const updateId = `offline_${Date.now()}_${currentUser.id}`;

      // Create offline update object for display
      const offlineUpdate: RealUpdate = {
        id: updateId,
        userId: currentUser.id,
        userName: currentUser.name || 'Unknown',
        userAvatar: currentUser.avatar,
        content: caption,
        caption,
        type,
        mediaUrl: mediaUri, // Store local URI for offline
        privacy: 'public',
        timestamp: new Date(),
        likes: [],
        comments: [],
        shares: [],
        views: [],
        downloads: [],
        location: undefined,
        tags: [],
        mentions: [],
        musicTrack: undefined,
      };

      // Convert to Update format for local storage
      const updateForStorage: Omit<Update, 'id'> & { id?: string } = {
        id: updateId,
        userId: currentUser.id,
        userName: currentUser.name || 'Unknown',
        userAvatar: currentUser.avatar,
        type: type === 'photo' ? 'image' : type,
        caption,
        timestamp: new Date(),
        isStory: false,
        expiresAt: undefined,
        privacy: 'public',
        isVisible: true,
        isArchived: false,
        location: undefined,
        hashtags: [],
        mentions: [],
        groupTags: [],
        media: mediaUri ? [{
          id: `media_${updateId}`,
          url: mediaUri,
          type: type === 'photo' ? 'image' : 'video',
          thumbnailUrl: undefined,
          width: undefined,
          height: undefined,
          duration: undefined,
          size: undefined,
        }] : [],
        likes: [],
        views: [],
        shares: [],
        downloads: [],
        reactions: [],
        comments: [],
        viewCount: 0,
        likeCount: 0,
        commentCount: 0,
        shareCount: 0,
        downloadCount: 0,
        isLikedByCurrentUser: false,
        isViewedByCurrentUser: false,
        isSharedByCurrentUser: false,
        isDownloadedByCurrentUser: false,
        isReported: false,
        reportCount: 0,
        isFlagged: false,
        isPinned: false,
        isHighlight: false,
      };

      // Store in local storage for immediate display
      await localUpdatesStorage.saveUpdate(updateForStorage);

      // Queue for automatic sync when connection is restored
      await updatesSyncService.queueForSync(updateForStorage as Update);

      // Add to current updates list for immediate UI update - FORCE IMMEDIATE DISPLAY
      setUpdates(prevUpdates => {
        // Make sure we don't duplicate if it already exists
        const filtered = prevUpdates.filter(u => u.id !== updateId);
        return [offlineUpdate, ...filtered];
      });

      console.log('✅ Offline update added to UI and queued for sync:', updateId);
      return { success: true, updateId };
    } catch (error) {
      console.error('❌ Error creating offline update:', error);
      return { success: false, error: 'Failed to save offline' };
    }
  };

  // Create update with offline support
  const createUpdate = async (mediaUri: string, type: UpdateType, caption: string, audioCaption?: AudioCaption, textOverlays?: TextOverlay[]) => {
    if (!currentUser?.id) return;

    try {
      setIsLoading(true);

      // Check network status first
      if (!isOnline) {
        // Handle offline creation immediately
        const offlineResult = await createOfflineUpdate(mediaUri, type, caption);
        if (offlineResult.success) {
          Alert.alert('Saved Offline', 'Your update will be posted when you\'re back online');
          setShowCreateModal(false);
          // Don't call loadUpdates() - we already added it to state directly
        } else {
          Alert.alert('Error', 'Failed to save update offline');
        }
        return;
      }

      // Online creation with permission error handling
      try {
        const result = await realUpdatesService.createUpdate(
          currentUser.id,
          currentUser.name || 'Unknown',
          currentUser.avatar,
          {
            type,
            caption,
            mediaUri: type === 'text' ? undefined : mediaUri, // Don't pass mediaUri for text updates
            privacy: 'public',
            isStory: false,
            audioCaption, // Add audio caption support
            textOverlays, // Add text overlays support
          }
        );

        if (result.success) {
          Alert.alert('Success!', 'Your update has been posted');
          setShowCreateModal(false);
          // Refresh feed to show new update
          await loadUpdates();
        } else {
          // If online creation fails, try offline
          const offlineResult = await createOfflineUpdate(mediaUri, type, caption);
          if (offlineResult.success) {
            Alert.alert('Saved Offline', 'Your update will be posted when connection improves');
            setShowCreateModal(false);
            // Don't call loadUpdates() - we already added it to state directly
          } else {
            Alert.alert('Error', result.error || 'Failed to create update');
          }
        }
      } catch (onlineError: any) {
        console.error('🔥 Online update creation failed:', onlineError?.message || onlineError);

        // If it's a permission error or any Firebase error, fallback to offline
        const offlineResult = await createOfflineUpdate(mediaUri, type, caption);
        if (offlineResult.success) {
          Alert.alert('Saved Offline', 'Your update will be posted when connection improves');
          setShowCreateModal(false);
          // Don't call loadUpdates() - we already added it to state directly
        } else {
          Alert.alert('Error', 'Failed to create update');
        }
      }
    } catch (error) {
      console.error('❌ Error creating update:', error);
      // Try offline as fallback
      try {
        const offlineResult = await createOfflineUpdate(mediaUri, type, caption);
        if (offlineResult.success) {
          Alert.alert('Saved Offline', 'Your update will be posted when you\'re back online');
          setShowCreateModal(false);
          // Don't call loadUpdates() - we already added it to state directly
        } else {
          Alert.alert('Error', 'Failed to create update');
        }
      } catch (offlineError) {
        Alert.alert('Error', 'Failed to create update');
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Enhanced Story Management Functions
  // Removed createStory function - pure video feed only

  // Removed all story management functions - pure video feed only

  // Removed tab switching functionality - pure video feed only

  // Simple scroll handler - no header animation
  const handleScroll = () => {
    // No header animation needed - header stays fixed
  };

  // Enhanced search functionality with online/offline support
  const performSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      let searchResults: RealUpdate[] = [];

      if (isOnline) {
        // Online search - enhanced search with potential for future remote search
        try {
          // Search in current updates with simple filtering
          searchResults = updates.filter(update => {
            const searchText = query.toLowerCase();
            const matchesCaption = update.caption?.toLowerCase().includes(searchText);
            const matchesUsername = update.userName.toLowerCase().includes(searchText);

            return matchesCaption || matchesUsername;
          });

          // TODO: Future enhancement - add remote search capability
          // const remoteResults = await realUpdatesService.searchUpdates(query);
          // searchResults = [...searchResults, ...remoteResults];
        } catch (onlineError) {
          console.warn('Online search failed, falling back to basic search:', onlineError);
          // Fallback to basic search if enhanced search fails
          searchResults = updates.filter(update => {
            const searchText = query.toLowerCase();
            const matchesCaption = update.caption?.toLowerCase().includes(searchText);
            const matchesUsername = update.userName.toLowerCase().includes(searchText);
            return matchesCaption || matchesUsername;
          });
        }
      } else {
        // Offline search - search only in locally cached updates
        searchResults = updates.filter(update => {
          const searchText = query.toLowerCase();
          const matchesCaption = update.caption?.toLowerCase().includes(searchText);
          const matchesUsername = update.userName.toLowerCase().includes(searchText);

          return matchesCaption || matchesUsername;
        });
      }

      // Sort results by relevance and recency
      searchResults.sort((a, b) => {
        // Prioritize exact matches in username
        const aUsernameMatch = a.userName.toLowerCase().includes(query.toLowerCase());
        const bUsernameMatch = b.userName.toLowerCase().includes(query.toLowerCase());

        if (aUsernameMatch && !bUsernameMatch) return -1;
        if (!aUsernameMatch && bUsernameMatch) return 1;

        // Then sort by timestamp (most recent first)
        return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
      });

      setSearchResults(searchResults);
    } catch (error) {
      console.error('Search error:', error);
      Alert.alert(
        'Search Error',
        isOnline
          ? 'Failed to search updates. Please try again.'
          : 'Search failed. Limited to cached updates while offline.'
      );
    } finally {
      setIsSearching(false);
    }
  };

  // Render update item - IRACHAT IMMERSIVE STYLE
  const renderUpdateItem = ({ item }: { item: RealUpdate }) => (
    <View style={styles.immersiveContainer}>
      {/* Full screen media background */}
      {item.mediaUrl && (
        <View style={styles.tiktokMediaContainer}>
          {item.type === 'photo' ? (
            <Image
              source={{ uri: item.mediaUrl }}
              style={styles.tiktokMedia}
              resizeMode="contain"
            />
          ) : item.type === 'video' && item.mediaUrl ? (
            <VideoPlayer
              key={`video-${item.id}-${item.mediaUrl}`}
              source={{ uri: item.mediaUrl }}
              style={styles.tiktokMedia}
              isLooping={true}
              videoId={item.id}
              isInViewport={currentlyPlayingVideo === item.id}
            />
          ) : item.type === 'video' ? (
            // Video without URL - show loading
            <View style={[styles.tiktokMedia, { backgroundColor: '#1a1a1a', justifyContent: 'center', alignItems: 'center' }]}>
              <Ionicons name="videocam-outline" size={64} color="#666" />
              <Text style={{ color: '#666', fontSize: 14, marginTop: 8 }}>
                Video Loading...
              </Text>
            </View>
          ) : null}

          {/* Gradient overlay for text readability */}
          <View style={styles.tiktokGradientOverlay} />

          {/* Text Overlays */}
          {item.textOverlays && item.textOverlays.length > 0 && (
            <TextOverlayRenderer
              textOverlays={item.textOverlays}
              mediaWidth={SCREEN_WIDTH}
              mediaHeight={SCREEN_HEIGHT}
            />
          )}
        </View>
      )}

      {/* Right side actions - TikTok style */}
      <View style={styles.tiktokRightActions}>


        {/* Like button - CLICKABLE */}
        <TouchableOpacity
          style={styles.immersiveActionButton}
          onPress={() => handleLike(item.id)}
          onLongPress={() => openInteractionPage(item.id, 'likes')}
        >
          <Ionicons
            name={item.likes.includes(currentUser?.id || '') ? "heart" : "heart-outline"}
            size={32}
            color={item.likes.includes(currentUser?.id || '') ? "#87CEEB" : "#FFFFFF"}
          />
          <TouchableOpacity onPress={() => openInteractionPage(item.id, 'likes')}>
            <Text style={styles.immersiveActionText}>
              {item.likes.length > 999 ? `${(item.likes.length / 1000).toFixed(1)}K` : item.likes.length}
            </Text>
          </TouchableOpacity>
        </TouchableOpacity>

        {/* Comment button */}
        <TouchableOpacity
          style={styles.immersiveActionButton}
          onPress={() => handleComment(item.id)}
        >
          <Ionicons name="chatbubble" size={28} color="#FFFFFF" />
          <Text style={styles.immersiveActionText}>{item.comments.length}</Text>
        </TouchableOpacity>

        {/* Share button - CLICKABLE */}
        <TouchableOpacity
          style={styles.immersiveActionButton}
          onPress={() => handleShare(item.id)}
          onLongPress={() => openInteractionPage(item.id, 'shares')}
        >
          <Ionicons name="arrow-redo" size={28} color="#FFFFFF" />
          <TouchableOpacity onPress={() => openInteractionPage(item.id, 'shares')}>
            <Text style={styles.immersiveActionText}>
              {item.shares.length > 999 ? `${(item.shares.length / 1000).toFixed(1)}K` : item.shares.length}
            </Text>
          </TouchableOpacity>
        </TouchableOpacity>

        {/* Download button - CLICKABLE with real-time progress */}
        <TouchableOpacity
          style={styles.immersiveActionButton}
          onPress={() => handleDownload(item.id)}
          onLongPress={() => openInteractionPage(item.id, 'downloads')}
          disabled={downloadingUpdates.has(item.id)}
        >
          {downloadingUpdates.has(item.id) ? (
            <View style={styles.downloadProgressContainer}>
              <ActivityIndicator size="small" color="#FFFFFF" />
              <Text style={styles.downloadProgressText}>
                {downloadProgress[item.id] || 0}%
              </Text>
            </View>
          ) : (
            <>
              <Ionicons
                name={item.downloads.includes(currentUser?.id || '') ? "checkmark-circle" : "download"}
                size={28}
                color={item.downloads.includes(currentUser?.id || '') ? "#87CEEB" : "#FFFFFF"}
              />
              <TouchableOpacity onPress={() => openInteractionPage(item.id, 'downloads')}>
                <Text style={styles.immersiveActionText}>
                  {item.downloads.includes(currentUser?.id || '') ? 'Saved' : 'Save'}
                </Text>
              </TouchableOpacity>
            </>
          )}
        </TouchableOpacity>

        {/* Delete button - Only show for update owner */}
        {currentUser?.id === item.userId && (
          <TouchableOpacity
            style={styles.immersiveActionButton}
            onPress={() => handleDeleteUpdate(item.id, item.userId)}
          >
            <Ionicons name="trash-outline" size={28} color="#FFFFFF" />
          </TouchableOpacity>
        )}
      </View>

      {/* Bottom content - TikTok style */}
      <View style={styles.tiktokBottomContent}>
        {/* Video Progress Bar - Only for videos */}
        {item.type === 'video' && item.mediaUrl && (
          <VideoProgressBar
            player={videoPlayerManager.getPlayer(item.id)}
            videoId={item.id}
            isVisible={currentlyPlayingVideo === item.id}
          />
        )}

        {/* User info */}
        <View style={styles.tiktokUserInfo}>
          <TouchableOpacity onPress={() => {
            // Navigate to user profile using router
            router.push(`/profile/${item.userId}`);
          }}>
            <Text style={styles.tiktokUserName}>@{item.userName}</Text>
          </TouchableOpacity>
          <Text style={styles.tiktokTimestamp}>{formatTimeAgo(item.timestamp)}</Text>
        </View>

        {/* Text Content for text updates OR Caption for media updates */}
        {item.type === 'text' ? (
          <View style={styles.textUpdateContent}>
            <Text style={styles.textUpdateText} numberOfLines={10}>
              {item.content}
            </Text>
          </View>
        ) : item.caption ? (
          <Text style={styles.tiktokCaption} numberOfLines={5}>
            {item.caption}
          </Text>
        ) : null}

        {/* Audio Caption Player for photo/text posts */}
        {item.audioCaption && (item.type === 'photo' || item.type === 'text') && (
          <AudioCaptionPlayer
            audioCaption={item.audioCaption}
            isInViewport={true} // Always consider in viewport for now, can be enhanced later
            autoPlay={true}
            style={{ marginTop: 8 }}
          />
        )}



        {/* View count - CLICKABLE */}
        <TouchableOpacity
          style={styles.immersiveViewCount}
          onPress={() => openInteractionPage(item.id, 'views')}
        >
          <Ionicons name="eye" size={16} color="#FFFFFF" />
          <Text style={styles.immersiveViewText}>
            {item.views.length > 1000
              ? `${(item.views.length / 1000).toFixed(1)}K views`
              : `${item.views.length} views`
            }
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  // Render empty state
  const renderEmptyState = () => (
    <View style={[styles.emptyContainer, { height: SCREEN_HEIGHT, backgroundColor: '#000000' }]}>
      <Ionicons name="camera-outline" size={64} color="#FFFFFF" />
      <Text style={[styles.emptyTitle, { color: '#FFFFFF' }]}>No Updates Yet</Text>
      <Text style={[styles.emptySubtitle, { color: '#FFFFFF' }]}>
        Share your first photo or video to get started
      </Text>
      <TouchableOpacity
        style={styles.createFirstButton}
        onPress={() => setShowCameraOptions(true)}
      >
        <Text style={styles.createFirstButtonText}>Create Update</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Pure TikTok-Style Video Feed Content - FULL SCREEN */}
      <View style={styles.contentContainer}>
        {isLoading && updates.length === 0 ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#667eea" />
            <Text style={styles.loadingText}>Loading updates...</Text>
          </View>
        ) : (
          <FlatList
            ref={flatListRef}
            data={updates}
            renderItem={renderUpdateItem}
            keyExtractor={(item, index) => `tiktok_${item.id}_${index}_${item.timestamp?.getTime() || Date.now()}`}
            style={styles.tiktokList}
            showsVerticalScrollIndicator={false}
            pagingEnabled={true} // TikTok-style pagination
            snapToInterval={SCREEN_HEIGHT} // Snap to full screen height
            snapToAlignment="start"
            decelerationRate="fast"
            onScroll={handleScroll}
            scrollEventThrottle={16}
            onViewableItemsChanged={onViewableItemsChanged}
            viewabilityConfig={viewabilityConfig}
            refreshControl={
              <RefreshControl
                refreshing={isRefreshing}
                onRefresh={handleRefresh}
                tintColor="#FFFFFF"
              />
            }
            ListEmptyComponent={renderEmptyState}
          />
        )}
      </View>

      {/* FLOATING OVERLAY HEADER ELEMENTS */}
      <View style={styles.floatingHeader}>
        {/* Floating Title */}
        <Text style={styles.floatingTitle}>Updates</Text>

        {/* Floating Actions */}
        <View style={styles.floatingActions}>
          <TouchableOpacity
            style={styles.floatingSearchButton}
            onPress={() => setShowSearch(true)}
          >
            <Ionicons name="search" size={24} color="#FFFFFF" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.floatingAvatarContainer}
            onPress={() => setShowCameraOptions(true)}
          >
            <Image
              source={{
                uri: currentUser?.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(currentUser?.name || 'You')}&background=87CEEB&color=fff`
              }}
              style={styles.floatingAvatar}
            />
            <View style={styles.floatingAddButton}>
              <Ionicons name="add" size={12} color="#FFFFFF" />
            </View>
          </TouchableOpacity>
        </View>
      </View>

      {/* Caption Input Modal */}
      <Modal
        visible={showCaptionModal}
        transparent
        animationType="none"
        onRequestClose={() => {
          console.log('📱 Caption modal closed via back button - resuming videos');
          setShowCaptionModal(false);
          setCaptionModalData(null);
          setCaptionText('');
          // Explicit resume (useEffect should handle this, but ensuring it works)
          setTimeout(() => setGlobalVideoPause(false), 100);
        }}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => {
            setShowCaptionModal(false);
            setCaptionModalData(null);
            setCaptionText('');
            setTimeout(() => setGlobalVideoPause(false), 100);
          }}
        >
          <KeyboardAvoidingView
            style={styles.modalOverlay}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
          >
            <TouchableOpacity
              style={styles.captionModal}
              activeOpacity={1}
              onPress={(e) => e.stopPropagation()}
            >

            {/* Media Preview */}
            {captionModalData && (
              <View style={styles.mediaPreviewContainer}>
                {captionModalData.type === 'photo' ? (
                  <Image
                    source={{ uri: captionModalData.mediaUri }}
                    style={styles.mediaPreview}
                    resizeMode="cover"
                  />
                ) : (
                  <View style={styles.videoPreviewContainer}>
                    <VideoPlayer
                      source={{ uri: captionModalData.mediaUri }}
                      style={styles.mediaPreview}
                      isLooping={true}
                      videoId="caption-preview"
                      isInViewport={true}
                    />

                    {/* Video trimming controls */}
                    <View style={styles.videoControls}>
                      <TouchableOpacity style={styles.trimButton}>
                        <Ionicons name="cut" size={20} color="#FFFFFF" />
                        <Text style={styles.trimButtonText}>Trim</Text>
                      </TouchableOpacity>

                      <View style={styles.playbackControls}>
                        <TouchableOpacity
                          style={styles.playbackButton}
                          onPress={() => setCaptionVideoPlaying(!captionVideoPlaying)}
                        >
                          <Ionicons
                            name={captionVideoPlaying ? "pause" : "play"}
                            size={16}
                            color="#FFFFFF"
                          />
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                )}
              </View>
            )}

            <TextInput
              style={styles.captionInput}
              placeholder="What's on your mind?"
              placeholderTextColor="#9CA3AF"
              value={captionText}
              onChangeText={setCaptionText}
              multiline
              numberOfLines={4}
              maxLength={500}
              autoFocus
              selectionColor="#87CEEB"
              cursorColor="#87CEEB"
            />

            {/* Character Counter */}
            <Text style={styles.characterCounter}>
              {captionText.length}/500
            </Text>

            <View style={styles.captionModalActions}>
              <TouchableOpacity
                style={styles.captionCancelButton}
                onPress={() => {
                  console.log('📱 Caption modal cancelled - closing and resuming videos');
                  setShowCaptionModal(false);
                  setCaptionModalData(null);
                  setCaptionText('');
                  // Explicit resume (useEffect should handle this, but ensuring it works)
                  setTimeout(() => setGlobalVideoPause(false), 100);
                }}
              >
                <Ionicons name="close" size={24} color="#6B7280" />
              </TouchableOpacity>

              {/* Removed story draft functionality */}

              <TouchableOpacity
                style={styles.captionSubmitButton}
                onPress={handleCaptionSubmit}
              >
                <Ionicons name="checkmark" size={24} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
            </TouchableOpacity>
          </KeyboardAvoidingView>
        </TouchableOpacity>
      </Modal>

      {/* Text Update Modal */}
      <Modal
        visible={showCreateModal}
        transparent
        animationType="none"
        onRequestClose={() => setShowCreateModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.cameraOptionsModal}>
            <Text style={styles.modalTitle}>Create Text Update</Text>

            <TouchableOpacity
              style={styles.optionButton}
              onPress={() => {
                setShowCreateModal(false);
                Alert.prompt(
                  'Text Update',
                  'What\'s on your mind?',
                  async (text) => {
                    if (text && text.trim()) {
                      await createUpdate('', 'text', text.trim());
                    }
                  },
                  'plain-text',
                  '',
                  'default'
                );
              }}
            >
              <Ionicons name="text" size={24} color="#667eea" />
              <Text style={styles.optionText}>Write Something</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => {
                setShowCreateModal(false);
                setTimeout(() => setGlobalVideoPause(false), 100);
              }}
            >
              <Ionicons name="close" size={24} color="#6B7280" />
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Camera Options Modal - Enhanced TikTok Style */}
      <Modal
        visible={showCameraOptions}
        transparent
        animationType="slide"
        onRequestClose={() => setShowCameraOptions(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.tiktokOptionsModal}>
            <View style={styles.modalHandle} />
            <Text style={styles.tiktokModalTitle}>Create Update</Text>

            <View style={styles.tiktokOptionsGrid}>
              <TouchableOpacity
                style={styles.tiktokOptionButton}
                onPress={() => handleCameraOption('camera')}
              >
                <View style={[styles.tiktokOptionIcon, { backgroundColor: '#667eea' }]}>
                  <Ionicons name="camera" size={28} color="white" />
                </View>
                <Text style={styles.tiktokOptionText}>Camera</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.tiktokOptionButton}
                onPress={() => handleCameraOption('gallery')}
              >
                <View style={[styles.tiktokOptionIcon, { backgroundColor: '#10b981' }]}>
                  <Ionicons name="images" size={28} color="white" />
                </View>
                <Text style={styles.tiktokOptionText}>Gallery</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.tiktokOptionButton}
                onPress={() => handleCameraOption('text')}
              >
                <View style={[styles.tiktokOptionIcon, { backgroundColor: '#f59e0b' }]}>
                  <Ionicons name="text" size={28} color="white" />
                </View>
                <Text style={styles.tiktokOptionText}>Text</Text>
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              style={styles.tiktokCancelButton}
              onPress={() => {
                setShowCameraOptions(false);
                setTimeout(() => setGlobalVideoPause(false), 100);
              }}
            >
              <Text style={styles.tiktokCancelText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Interaction Pages Modal */}
      <UpdatesInteractionPages
        visible={showInteractionPages}
        onClose={() => {
          console.log('📱 Interaction pages closed - resuming videos');
          setShowInteractionPages(false);
          // Explicit resume (useEffect should handle this, but ensuring it works)
          setTimeout(() => setGlobalVideoPause(false), 100);
        }}
        updateId={selectedUpdateId}
        initialTab={selectedInteractionTab}
        onUserPress={handleUserPress}
      />

      {/* Comments Page Modal */}
      <UpdatesCommentsPage
        visible={showCommentsPage}
        onClose={() => {
          console.log('📱 Comments page closed - resuming videos');
          setShowCommentsPage(false);
          // Explicit resume (useEffect should handle this, but ensuring it works)
          setTimeout(() => setGlobalVideoPause(false), 100);
        }}
        updateId={commentsUpdateId}
        onUserPress={handleUserPress}
      />

      {/* Removed Story Creator Modal - pure video feed only */}


      {/* Search Modal */}
      <Modal
        visible={showSearch}
        animationType="none"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowSearch(false)}
      >
        <View style={styles.searchContainer}>
          <View style={styles.searchHeader}>
            <View style={styles.searchHeaderContent}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => setShowSearch(false)}
              >
                <Ionicons name="arrow-back" size={24} color="#F9FAFB" />
              </TouchableOpacity>
              <View style={styles.searchInputContainer}>
                <Ionicons name="search" size={20} color="#D1D5DB" style={styles.searchIcon} />
                <TextInput
                  style={styles.searchInput}
                  placeholder="Search updates..."
                  placeholderTextColor="#9CA3AF"
                  value={searchQuery}
                  onChangeText={(text: string) => {
                    setSearchQuery(text);
                    performSearch(text);
                  }}
                  autoFocus
                  returnKeyType="search"
                  onSubmitEditing={() => performSearch(searchQuery)}
                />
                {searchQuery.length > 0 && (
                  <TouchableOpacity
                    onPress={() => {
                      setSearchQuery('');
                      setSearchResults([]);
                    }}
                  >
                    <Ionicons name="close-circle" size={20} color="#D1D5DB" />
                  </TouchableOpacity>
                )}
              </View>
            </View>
          </View>

          {isSearching ? (
            <View style={styles.searchLoadingContainer}>
              <ActivityIndicator size="large" color="#667eea" />
              <Text style={styles.searchLoadingText}>Searching...</Text>
            </View>
          ) : searchResults.length > 0 ? (
            <FlatList
              data={searchResults}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.searchResultItem}
                  onPress={() => {
                    setShowSearch(false);
                    // Find index of this update in the main updates list
                    const index = updates.findIndex(update => update.id === item.id);
                    if (index !== -1) {
                      // Scroll to this update in the main list
                      // This would require a ref to the FlatList
                    }
                  }}
                >
                  <Image
                    source={{
                      uri: item.mediaUrl || item.userAvatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(item.userName)}&background=87CEEB&color=fff`
                    }}
                    style={styles.searchResultImage}
                  />
                  <View style={styles.searchResultContent}>
                    <Text style={styles.searchResultUsername}>{item.userName}</Text>
                    <Text style={styles.searchResultCaption} numberOfLines={2}>
                      {item.caption || 'No caption'}
                    </Text>
                    <Text style={styles.searchResultTime}>{formatTimeAgo(item.timestamp)}</Text>
                  </View>
                  <Ionicons name="chevron-forward" size={20} color="#D1D5DB" />
                </TouchableOpacity>
              )}
              keyExtractor={(item, index) => `search_${item.id}_${index}_${item.timestamp?.getTime() || Date.now()}`}
            />
          ) : searchQuery.length > 0 ? (
            <View style={styles.searchEmptyContainer}>
              <Ionicons name="search-outline" size={64} color="#E5E7EB" />
              <Text style={styles.searchEmptyText}>No results found</Text>
              <Text style={styles.searchEmptySubtext}>Try different keywords or filters</Text>
            </View>
          ) : (
            <View style={styles.searchInitialContainer}>
              <Ionicons name="search" size={64} color="#E5E7EB" />
              <Text style={styles.searchInitialText}>Search for updates</Text>
              <Text style={styles.searchInitialSubtext}>Find updates by caption, username, or content</Text>
            </View>
          )}
        </View>
      </Modal>

      {/* Video Trimmer Modal */}
      <Modal
        visible={showVideoTrimmer}
        transparent={false}
        animationType="none"
        onRequestClose={handleEditingCancel}
      >
        {captionModalData && (
          <VideoTrimmer
            visible={showVideoTrimmer}
            videoUri={captionModalData.mediaUri}
            onTrimComplete={handleVideoTrimComplete}
            onClose={handleEditingCancel}
            maxDuration={60}
          />
        )}
      </Modal>

      {/* Photo Cropper Modal */}
      <Modal
        visible={showPhotoCropper}
        transparent={false}
        animationType="none"
        onRequestClose={handleEditingCancel}
      >
        {captionModalData && (
          <PhotoCropper
            imageUri={captionModalData.mediaUri}
            onCropComplete={handlePhotoCropComplete}
            onCancel={handleEditingCancel}
          />
        )}
      </Modal>

      {/* TikTok-Style Media Picker */}
      <TikTokStyleMediaPicker
        visible={showTikTokMediaPicker}
        onClose={() => setShowTikTokMediaPicker(false)}
        onMediaSelected={handleTikTokMediaSelected}
        onCameraPress={handleTikTokCameraPress}
        onTextPress={handleTikTokTextPress}
      />

      {/* TikTok-Style Camera */}
      <TikTokStyleCamera
        visible={showTikTokCamera}
        onClose={() => setShowTikTokCamera(false)}
        onMediaCaptured={handleTikTokCameraCapture}
      />

      {/* Media Preview Editor */}
      {selectedMediaForPreview && (
        <MediaPreviewEditor
          visible={showMediaPreviewEditor}
          mediaUri={selectedMediaForPreview.uri}
          mediaType={selectedMediaForPreview.type}
          onClose={handleCloseMediaPreview}
          onPost={handleMediaPreviewPost}
          isPosting={isPostingUpdate}
        />
      )}

      {/* Text Update Creator */}
      <TextUpdateCreator
        visible={showTextUpdateCreator}
        onClose={handleCloseTextCreator}
        onPost={handleTextUpdatePost}
        isPosting={isPostingUpdate}
      />

    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000', // TikTok black background
  },
  // FLOATING OVERLAY HEADER STYLES
  floatingHeader: {
    position: 'absolute',
    top: DeviceInfo.statusBarHeight + 30, // Even more spacing from status bar
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    zIndex: 1000,
  },
  floatingTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textShadowColor: 'rgba(0, 0, 0, 0.8)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  floatingActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 15,
  },
  floatingSearchButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  floatingAvatarContainer: {
    position: 'relative',
  },
  floatingAvatar: {
    width: 44,
    height: 44,
    borderRadius: 22,
    borderWidth: 2,
    borderColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  floatingAddButton: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#87CEEB',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },

  // IRACHAT IMMERSIVE STYLE COMPONENTS
  immersiveContainer: {
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT,
    position: 'relative',
    backgroundColor: '#000000',
  },
  tiktokContainer: {
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT,
    position: 'relative',
    backgroundColor: '#000000',
  },
  tiktokMediaContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
    overflow: 'hidden', // Ensure media doesn't go beyond container
  },
  tiktokMedia: {
    width: '100%',
    height: '100%',
    backgroundColor: '#000000',
  },
  videoPauseOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.3)',
    zIndex: 10,
  },
  videoMuteIndicator: {
    position: 'absolute',
    top: 20,
    right: 20,
    backgroundColor: 'rgba(0,0,0,0.6)',
    borderRadius: 20,
    padding: 8,
    zIndex: 10,
  },
  likeAnimationOverlay: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    marginTop: -30, // Half of icon size
    marginLeft: -30, // Half of icon size
    zIndex: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cooldownMessageOverlay: {
    position: 'absolute',
    bottom: 100,
    left: 20,
    right: 100, // Don't cover the right action buttons
    backgroundColor: 'rgba(0,0,0,0.7)',
    borderRadius: 20,
    padding: 12,
    zIndex: 15,
    alignItems: 'center',
  },
  cooldownMessageText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  videoTapOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 80, // Don't cover the right action buttons area
    bottom: 0,
    zIndex: 5, // Above video, below pause/mute indicators
  },
  tiktokGradientOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 200,
    backgroundColor: 'rgba(0,0,0,0.4)', // Gradient effect for React Native
    zIndex: 2,
  },
  tiktokRightActions: {
    position: 'absolute',
    right: 12,
    bottom: 90, // Reduced to match bottom content positioning
    zIndex: 3,
    alignItems: 'center',
  },
  tiktokAvatarContainer: {
    position: 'relative',
    marginBottom: 20,
  },
  tiktokAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  tiktokFollowButton: {
    position: 'absolute',
    bottom: -8,
    left: '50%',
    marginLeft: -12,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#87CEEB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  immersiveActionButton: {
    alignItems: 'center',
    marginBottom: 20,
    padding: 8,
  },

  immersiveActionText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    marginTop: 4,
    textAlign: 'center',
  },
  tiktokActionButton: {
    alignItems: 'center',
    marginBottom: 20,
    padding: 8,
  },
  tiktokActionText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    marginTop: 4,
    textAlign: 'center',
  },
  tiktokBottomContent: {
    position: 'absolute',
    bottom: 90, // Reduced from 100 to 90 for less space above tab bar
    left: 0,
    right: 80, // Leave space for right actions
    padding: 16,
    zIndex: 3,
  },
  tiktokUserInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  tiktokUserName: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '700',
    marginRight: 12,
  },
  tiktokTimestamp: {
    color: '#FFFFFF',
    fontSize: 12,
    opacity: 0.8,
  },
  tiktokCaption: {
    color: '#FFFFFF',
    fontSize: 15,
    lineHeight: 20,
    marginBottom: 8,
    flexShrink: 1, // Allow caption to shrink and expand upward
    textAlign: 'left',
  },

  tiktokViewCount: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  immersiveViewCount: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: 'rgba(0,0,0,0.3)',
    borderRadius: 12,
  },
  immersiveViewText: {
    color: '#FFFFFF',
    fontSize: 12,
    marginLeft: 4,
    opacity: 0.9,
    fontWeight: '500',
  },

  tiktokList: {
    flex: 1,
  },

  // Tab Bar Styles
  tabBar: {
    backgroundColor: '#000000',
    paddingTop: 100, // Account for header height
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeTab: {
    // Active tab styling handled by indicator
  },
  tabText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#9CA3AF',
    textAlign: 'center',
  },
  activeTabText: {
    color: '#FFFFFF',
  },
  tabIndicator: {
    position: 'absolute',
    bottom: 0,
    left: 16,
    width: SCREEN_WIDTH / 2 - 16,
    height: 3,
    backgroundColor: '#667eea',
    borderRadius: 2,
  },

  // Content Container
  contentContainer: {
    flex: 1,
    backgroundColor: '#000000',
  },

  // Stories Content Styles
  storiesContent: {
    flex: 1,
    backgroundColor: '#000000',
  },
  storiesHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  storiesTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  addStoryButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#667eea',
    justifyContent: 'center',
    alignItems: 'center',
  },
  storiesScrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  storiesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingVertical: 16,
  },
  storyGridItem: {
    width: (SCREEN_WIDTH - 48) / 2,
    height: 200,
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#1a1a1a',
  },
  storyGridImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  storyGridOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0,0,0,0.6)',
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  storyGridTime: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
  },
  emptyStoriesContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 64,
  },
  emptyStoriesText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#FFFFFF',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStoriesSubtext: {
    fontSize: 16,
    color: '#9CA3AF',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  createStoryButton: {
    backgroundColor: '#667eea',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 20,
  },
  createStoryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.7,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    backgroundColor: '#667eea',
    borderBottomWidth: 1,
    borderBottomColor: '#5a67d8',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 45, // Reduced for compact header
    paddingBottom: 15, // Reduced bottom padding
    minHeight: 110, // Match the reduced header height
    flex: 1, // Take full height of header
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    lineHeight: 28, // Better line height for vertical alignment
  },
  createButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#667eea',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerAvatarContainer: {
    position: 'relative',
    width: 36,
    height: 36,
    marginTop: 2,
  },
  headerAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  headerAddButton: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#667eea',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#FFFFFF',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  searchButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6B7280',
  },
  list: {
    flex: 1,
  },
  listContent: {
    paddingVertical: 8,
  },
  updateItem: {
    backgroundColor: '#FFFFFF',
    marginBottom: 8,
    paddingVertical: 16,
  },
  updateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
  },
  timestamp: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 2,
  },
  moreButton: {
    padding: 8,
  },
  mediaContainer: {
    width: SCREEN_WIDTH,
    height: SCREEN_WIDTH * 1.2,
    backgroundColor: '#000000',
  },
  media: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  caption: {
    fontSize: 14,
    color: '#374151',
    paddingHorizontal: 16,
    paddingVertical: 12,
    lineHeight: 20,
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 24,
  },
  actionCount: {
    fontSize: 14,
    color: '#6B7280',
    marginLeft: 4,
    fontWeight: '500',
  },
  viewCount: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 'auto',
  },
  viewCountText: {
    fontSize: 12,
    color: '#9CA3AF',
    marginLeft: 4,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 64,
  },
  createFirstButton: {
    backgroundColor: '#667eea',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  createFirstButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
    paddingHorizontal: 2,
  },
  cameraOptionsModal: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingVertical: 2,
    paddingHorizontal: 2,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    textAlign: 'center',
    marginBottom: 2,
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 8,
    backgroundColor: '#F9FAFB',
  },
  optionText: {
    fontSize: 16,
    color: '#374151',
    marginLeft: 16,
    fontWeight: '500',
  },
  cancelButton: {
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#6B7280',
    fontWeight: '600',
  },

  // Caption Modal Styles
  captionModal: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingVertical: 16,
    paddingHorizontal: 16,
    maxHeight: '85%',
    minHeight: '50%',
    marginHorizontal: 2,
  },
  mediaPreviewContainer: {
    width: '100%',
    height: 250,
    borderRadius: 12,
    overflow: 'hidden',
    marginVertical: 12,
    backgroundColor: '#000000',
  },
  mediaPreview: {
    width: '100%',
    height: '100%',
  },
  captionInput: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 8,
    fontSize: 16,
    color: '#374151',
    backgroundColor: '#F9FAFB',
    textAlignVertical: 'top',
    minHeight: 120,
    marginBottom: 2,
  },
  characterCounter: {
    fontSize: 12,
    color: '#9CA3AF',
    textAlign: 'right',
    marginBottom: 2,
  },
  captionModalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 16,
    paddingBottom: 20,
  },
  captionCancelButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
    flex: 1,
    marginRight: 8,
  },
  captionCancelText: {
    fontSize: 16,
    color: '#6B7280',
    fontWeight: '600',
    textAlign: 'center',
  },
  captionDraftButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    backgroundColor: '#FEF3C7',
    borderWidth: 1,
    borderColor: '#F59E0B',
    flex: 1,
    marginHorizontal: 4,
  },
  captionDraftText: {
    fontSize: 16,
    color: '#D97706',
    fontWeight: '600',
    textAlign: 'center',
  },
  captionSubmitButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    backgroundColor: '#667eea',
    flex: 1,
    marginLeft: 8,
  },
  captionSubmitText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
    textAlign: 'center',
  },


  avatarAddStoryButton: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#87CEEB',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },


  // Story Viewer Styles
  storyViewerContainer: {
    flex: 1,
    backgroundColor: '#000000',
  },
  storyContent: {
    flex: 1,
    position: 'relative',
  },
  storyMedia: {
    width: '100%',
    height: '100%',
  },
  storyHeader: {
    position: 'absolute',
    top: 60,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    zIndex: 10,
  },
  storyUserInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  storyUserAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 8,
  },
  storyUserName: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  storyTime: {
    color: '#FFFFFF',
    fontSize: 12,
    opacity: 0.8,
  },
  storyCloseButton: {
    padding: 8,
  },
  storyCaptionContainer: {
    position: 'absolute',
    bottom: 100,
    left: 16,
    right: 16,
    zIndex: 10,
  },
  storyCaption: {
    color: '#FFFFFF',
    fontSize: 16,
    lineHeight: 22,
    backgroundColor: 'rgba(0,0,0,0.3)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  storyNavigation: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    zIndex: 5,
  },
  storyNavButton: {
    position: 'absolute',
    left: 16,
    top: '50%',
    marginTop: -20,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  storyNavButtonRight: {
    left: 'auto',
    right: 16,
  },

  // Draft Styles
  draftContainer: {
    backgroundColor: '#F0F9FF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#BAE6FD',
  },
  draftTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0369A1',
    marginBottom: 4,
  },
  draftInfo: {
    fontSize: 14,
    color: '#0284C7',
    marginBottom: 12,
    lineHeight: 18,
  },
  restoreDraftButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#667eea',
  },
  restoreDraftText: {
    fontSize: 14,
    color: '#667eea',
    fontWeight: '600',
    marginLeft: 4,
  },

  // Download progress styles
  downloadProgressContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 40,
  },
  downloadProgressText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
    marginTop: 2,
    textAlign: 'center',
  },

  // Search Modal Styles
  searchContainer: {
    flex: 1,
    backgroundColor: '#1F2937', // Gray-black background
  },
  searchHeader: {
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 16,
    height: 90,
    backgroundColor: '#374151', // Dark gray header
    borderBottomWidth: 1,
    borderBottomColor: '#4B5563',
    justifyContent: 'flex-end', // Push content to bottom
  },
  searchHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    height: 40, // Fixed height for proper centering
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4B5563',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 8,
    borderWidth: 1,
    borderColor: '#6B7280',
    height: 40,
  },
  searchIcon: {
    marginRight: 4,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#F9FAFB',
    paddingVertical: 0,
    paddingHorizontal: 0,
    textAlignVertical: 'center',
    includeFontPadding: false,
    height: 24,
  },
  searchLoadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
    backgroundColor: '#1F2937',
  },
  searchLoadingText: {
    fontSize: 16,
    color: '#9CA3AF',
    fontWeight: '500',
  },
  searchResultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#374151',
    marginHorizontal: 12,
    marginVertical: 4,
    borderRadius: 12,
    gap: 12,
    borderWidth: 1,
    borderColor: '#4B5563',
  },
  searchResultImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    backgroundColor: '#4B5563',
    borderWidth: 1,
    borderColor: '#6B7280',
  },
  searchResultContent: {
    flex: 1,
    gap: 6,
  },
  searchResultUsername: {
    fontSize: 16,
    fontWeight: '600',
    color: '#F9FAFB',
  },
  searchResultCaption: {
    fontSize: 14,
    color: '#D1D5DB',
    lineHeight: 20,
  },
  searchResultTime: {
    fontSize: 12,
    color: '#9CA3AF',
    fontWeight: '500',
  },
  searchEmptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
    paddingHorizontal: 32,
    backgroundColor: '#1F2937',
  },
  searchEmptyText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#F9FAFB',
    textAlign: 'center',
  },
  searchEmptySubtext: {
    fontSize: 16,
    color: '#9CA3AF',
    textAlign: 'center',
    lineHeight: 24,
  },
  searchInitialContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
    paddingHorizontal: 32,
    backgroundColor: '#1F2937',
  },
  searchInitialText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#F9FAFB',
    textAlign: 'center',
  },
  searchInitialSubtext: {
    fontSize: 16,
    color: '#9CA3AF',
    textAlign: 'center',
    lineHeight: 24,
  },

  // Video preview and trimming styles
  videoPreviewContainer: {
    position: 'relative',
  },
  videoControls: {
    position: 'absolute',
    bottom: 10,
    left: 10,
    right: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 8,
    padding: 8,
  },
  trimButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#667eea',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  trimButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  playbackControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  playbackButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // TikTok-style Modal Styles
  tiktokOptionsModal: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingVertical: 20,
    paddingHorizontal: 20,
    minHeight: 280,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  modalHandle: {
    width: 40,
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
    alignSelf: 'center',
    marginBottom: 20,
  },
  tiktokModalTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 24,
  },
  tiktokOptionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 24,
  },
  tiktokOptionButton: {
    alignItems: 'center',
    gap: 12,
  },
  tiktokOptionIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tiktokOptionText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
  },
  tiktokCancelButton: {
    backgroundColor: '#F3F4F6',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  tiktokCancelText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B7280',
  },

  // Text Update Styles
  textUpdateContent: {
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 12,
    padding: 16,
    marginTop: 8,
    maxWidth: '90%',
  },
  textUpdateText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '500',
    lineHeight: 24,
  },

  // Video Progress Bar Styles
  videoProgressContainer: {
    position: 'absolute',
    top: -2, // Position at the top edge of the bottom bar
    left: 0,
    right: 80, // Match the bottom content right margin (leave space for right actions)
    height: 20, // Larger touch area for better interaction
    justifyContent: 'center',
    zIndex: 10,
    paddingHorizontal: 16, // Match the bottom content padding
  },
  videoProgressTrack: {
    height: 2, // Exactly 2 pixels thick
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 1,
    width: '100%',
  },
  videoProgressFill: {
    height: 2, // Exactly 2 pixels thick
    backgroundColor: '#87CEEB', // Sky blue color
    borderRadius: 1,
  },
});

export default UpdatesScreen;

