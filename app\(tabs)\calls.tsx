// 📞 REAL CALLS TAB - Fully functional calling with WebRTC
// Real call history, contact integration, and actual calling functionality

import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";

import React, { useCallback, useEffect, useState, useRef } from "react";
import {
  ActivityIndicator,
  Alert,
  FlatList,
  Image,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  StyleSheet,
  RefreshControl,
  Dimensions,
} from "react-native";
import { Gesture, GestureDetector, GestureHandlerRootView } from "react-native-gesture-handler";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  runOnJS,
} from "react-native-reanimated";

import { useSelector } from "react-redux";
import { RootState } from "../../src/redux/store";
import { realCallService, CallLog } from "../../src/services/realCallService";
import { contactService, IraChatContact } from "../../src/services/contactService";
import { navigationService } from "../../src/services/navigationService";
import { FloatingActionButton, QuickNavActions } from "../../src/components/NavigationHelper";
import { formatCallTime, formatCallDuration } from "../../src/utils/dateUtils";
import { ResponsiveContainer } from "../../src/components/ui/ResponsiveContainer";
import { ResponsiveCard, ResponsiveListCard } from "../../src/components/ui/ResponsiveCard";
import { ResponsiveScale, ComponentSizes, ResponsiveTypography, ResponsiveSpacing, DeviceInfo } from "../../src/utils/responsiveUtils";

export default function CallsScreen() {
  const router = useRouter();
  const currentUser = useSelector((state: RootState) => state.user.currentUser);

  // Use React for potential future ref usage
  const componentRef = React.useRef(null);

  // Use design system constants for consistent styling
  const responsiveUtils = { ComponentSizes, ResponsiveTypography, ResponsiveSpacing, DeviceInfo };

  // Apply responsive design constants
  const responsiveStyles = {
    searchIconSize: responsiveUtils.ComponentSizes.fabIconSize,
    headerSpacing: responsiveUtils.ResponsiveSpacing.lg,
    cardPadding: responsiveUtils.ResponsiveSpacing.md,
    typography: responsiveUtils.ResponsiveTypography,
    deviceInfo: responsiveUtils.DeviceInfo,
    iconScale: ResponsiveScale.iconSize(20),
    fontScale: ResponsiveScale.fontScale(16),
    spacingScale: ResponsiveScale.spacing(12),
  };



  const [contacts, setContacts] = useState<IraChatContact[]>([]);
  const [callHistory, setCallHistory] = useState<CallLog[]>([]);
  const [filteredContacts, setFilteredContacts] = useState<IraChatContact[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<"contacts" | "history">("history");
  const [showSearch, setShowSearch] = useState(false);

  // Swipe navigation setup
  const { width: SCREEN_WIDTH } = Dimensions.get('window');
  const translateX = useSharedValue(0);
  const tabIndicatorPosition = useSharedValue(0);
  const tabs = ['history', 'contacts'] as const;

  // Swipe navigation functions
  const switchToTab = (tabName: typeof tabs[number]) => {
    const tabIndex = tabs.indexOf(tabName);
    setActiveTab(tabName);

    // Animate tab indicator with smooth spring
    tabIndicatorPosition.value = withSpring(tabIndex, {
      damping: 18,
      stiffness: 180,
      mass: 0.9,
    });
  };



  // Create smooth pan gesture with better physics and FlatList compatibility
  const panGesture = Gesture.Pan()
    .minDistance(10) // Require minimum distance to start gesture
    .activeOffsetX([-10, 10]) // Only activate for horizontal swipes
    .failOffsetY([-20, 20]) // Fail if vertical movement is too large
    .onUpdate((event) => {
      // Only respond to primarily horizontal gestures
      if (Math.abs(event.velocityY) > Math.abs(event.velocityX) * 2) {
        return; // Let FlatList handle vertical scrolling
      }

      // Smooth, responsive translation with damping
      const currentIndex = tabs.indexOf(activeTab);
      const maxTranslation = SCREEN_WIDTH * 0.6;

      // Apply resistance at boundaries
      let translation = event.translationX;

      if (currentIndex === 0 && translation > 0) {
        // At first tab, add resistance to right swipe
        translation = translation * 0.2;
      } else if (currentIndex === tabs.length - 1 && translation < 0) {
        // At last tab, add resistance to left swipe
        translation = translation * 0.2;
      }

      // Clamp translation to prevent excessive movement
      translateX.value = Math.max(-maxTranslation, Math.min(maxTranslation, translation));
    })
    .onEnd((event) => {
      const { translationX, velocityX } = event;
      const currentIndex = tabs.indexOf(activeTab);

      // More sensitive thresholds for smoother experience
      const swipeThreshold = SCREEN_WIDTH * 0.12; // Even more sensitive
      const velocityThreshold = 250; // Lower velocity threshold

      // Determine if we should switch tabs
      let shouldSwitch = false;
      let targetTab = activeTab;

      if (Math.abs(translationX) > swipeThreshold || Math.abs(velocityX) > velocityThreshold) {
        if (translationX > 0 && currentIndex > 0) {
          // Swipe right - go to previous tab
          targetTab = tabs[currentIndex - 1];
          shouldSwitch = true;
        } else if (translationX < 0 && currentIndex < tabs.length - 1) {
          // Swipe left - go to next tab
          targetTab = tabs[currentIndex + 1];
          shouldSwitch = true;
        }
      }

      if (shouldSwitch) {
        // Switch tab with smooth animation
        runOnJS(switchToTab)(targetTab);
      }

      // Always animate back to center with smooth spring
      translateX.value = withSpring(0, {
        damping: 25,
        stiffness: 250,
        mass: 0.7,
      });
    });

  const animatedContentStyle = useAnimatedStyle(() => {
    const progress = Math.abs(translateX.value) / (SCREEN_WIDTH * 0.3);
    const clampedProgress = Math.min(progress, 1);

    return {
      transform: [
        {
          translateX: translateX.value * 0.3 // More responsive movement
        },
        {
          scale: 1 - clampedProgress * 0.02 // Subtle scale effect
        }
      ],
      opacity: 1 - clampedProgress * 0.1, // Subtle fade during swipe
    };
  });

  const animatedIndicatorStyle = useAnimatedStyle(() => {
    const basePosition = tabIndicatorPosition.value * (SCREEN_WIDTH / 2);

    // Add real-time swipe feedback to indicator
    const swipeOffset = translateX.value * 0.05; // Subtle indicator movement

    return {
      transform: [
        {
          translateX: basePosition + swipeOffset,
        },
      ],
    };
  });

  // Load call history
  const loadCallHistory = useCallback(async () => {
    if (!currentUser?.id) return;
    
    try {
      setIsLoading(true);
      const history = await realCallService.getCallHistory(currentUser.id);
      setCallHistory(history);
      console.log('✅ Loaded call history:', history.length);
    } catch (error) {
      console.error('❌ Error loading call history:', error);
    } finally {
      setIsLoading(false);
    }
  }, [currentUser?.id]);

  // Load contacts
  const loadContacts = useCallback(async () => {
    if (!currentUser?.id) return;
    
    try {
      const userContacts = await contactService.getUserContacts(currentUser.id);
      setContacts(userContacts);
      setFilteredContacts(userContacts);
      console.log('✅ Loaded contacts:', userContacts.length);
    } catch (error) {
      console.error('❌ Error loading contacts:', error);
    }
  }, [currentUser?.id]);

  // Load data on component mount
  useEffect(() => {
    if (currentUser?.id) {
      loadCallHistory();
      loadContacts();
    }
  }, [currentUser?.id, loadCallHistory, loadContacts]);

  // Optimized search function
  const performSearch = useCallback((query: string) => {
    if (!query.trim()) {
      setFilteredContacts(contacts);
    } else {
      const filtered = contacts.filter(contact =>
        contact.name.toLowerCase().includes(query.toLowerCase()) ||
        contact.phoneNumber.includes(query)
      );
      setFilteredContacts(filtered);
    }
  }, [contacts]);

  // Filter contacts based on search with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      performSearch(searchQuery);
    }, 150); // 150ms debounce

    return () => clearTimeout(timeoutId);
  }, [searchQuery, performSearch]);

  // Refresh data
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    await Promise.all([loadCallHistory(), loadContacts()]);
    setIsRefreshing(false);
  }, [loadCallHistory, loadContacts]);

  // Handle voice call
  const handleVoiceCall = async (contact: IraChatContact) => {
    if (!currentUser?.id) return;
    
    try {
      console.log('📞 Starting voice call with:', contact.name);
      
      const result = await realCallService.startCall(
        currentUser.id,
        currentUser.name || 'Unknown',
        contact.userId || contact.id,
        contact.name,
        'voice'
      );
      
      if (result.success) {
        navigationService.startVoiceCall(contact.userId || contact.id, contact.name);
      } else {
        Alert.alert('Call Failed', result.error || 'Unable to start call');
      }
    } catch (error) {
      console.error('❌ Error starting voice call:', error);
      Alert.alert('Call Failed', 'Unable to start call');
    }
  };

  // Handle video call
  const handleVideoCall = async (contact: IraChatContact) => {
    if (!currentUser?.id) return;
    
    try {
      console.log('📹 Starting video call with:', contact.name);
      
      const result = await realCallService.startCall(
        currentUser.id,
        currentUser.name || 'Unknown',
        contact.userId || contact.id,
        contact.name,
        'video'
      );
      
      if (result.success) {
        navigationService.startVideoCall(contact.userId || contact.id, contact.name);
      } else {
        Alert.alert('Call Failed', result.error || 'Unable to start call');
      }
    } catch (error) {
      console.error('❌ Error starting video call:', error);
      Alert.alert('Call Failed', 'Unable to start call');
    }
  };

  // Render call history item - IRACHAT MODERN STYLE
  const renderCallHistoryItem = ({ item }: { item: CallLog }) => (
    <ResponsiveListCard
      onPress={() => {
        const contact = contacts.find(c => c.id === item.contactId);
        if (contact) {
          handleVoiceCall(contact);
        }
      }}
    >
      {/* Avatar with status indicator */}
      <View style={styles.modernAvatarContainer}>
        <TouchableOpacity onPress={() => {
          // Navigate to contact profile using router
          router.push(`/profile/${item.contactId}?name=${encodeURIComponent(item.contactName)}`);
        }}>
          <Image
            source={{
              uri: item.contactAvatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(item.contactName)}&background=87CEEB&color=fff`
            }}
            style={styles.modernAvatar}
          />
        </TouchableOpacity>
        {/* Call type indicator */}
        <View style={[
          styles.callTypeIndicator,
          { backgroundColor: item.type === 'video' ? '#87CEEB' : '#87CEEB' }
        ]}>
          <Ionicons
            name={item.type === 'video' ? 'videocam' : 'call'}
            size={12}
            color="#FFFFFF"
          />
        </View>
      </View>

      {/* Call info - IraChat modern layout */}
      <View style={styles.modernCallInfo}>
        <View style={styles.modernCallHeader}>
          <TouchableOpacity onPress={() => {
            // Navigate to contact profile using router
            router.push(`/profile/${item.contactId}?name=${encodeURIComponent(item.contactName)}`);
          }}>
            <Text style={styles.modernContactName}>{item.contactName}</Text>
          </TouchableOpacity>
          <Text style={styles.modernCallTime}>{formatCallTime(item.timestamp)}</Text>
        </View>

        <View style={styles.modernCallDetails}>
          <View style={styles.modernCallStatus}>
            <Ionicons
              name={
                item.direction === 'outgoing' ? 'arrow-up' :
                item.status === 'missed' ? 'arrow-down' : 'arrow-down'
              }
              size={14}
              color={
                item.status === 'missed' ? '#FF4444' :
                item.direction === 'outgoing' ? '#87CEEB' : '#87CEEB'
              }
              style={[
                styles.modernCallIcon,
                item.direction === 'outgoing' && { transform: [{ rotate: '45deg' }] },
                item.direction === 'incoming' && { transform: [{ rotate: '-45deg' }] }
              ]}
            />
            <Text style={[
              styles.modernCallStatusText,
              item.status === 'missed' && { color: '#FF4444' }
            ]}>
              {item.status === 'missed' ? 'Missed' :
               item.direction === 'outgoing' ? 'Outgoing' : 'Incoming'}
            </Text>
          </View>

          {item.duration && item.duration > 0 && (
            <Text style={styles.modernDuration}>
              {formatCallDuration(item.duration)}
            </Text>
          )}
        </View>
      </View>

      {/* Action buttons - IraChat modern style */}
      <View style={styles.modernCallActions}>
        <TouchableOpacity
          style={styles.modernCallButton}
          onPress={(e) => {
            e.stopPropagation();
            const contact = contacts.find(c => c.id === item.contactId);
            if (contact) {
              handleVoiceCall(contact);
            }
          }}
        >
          <Ionicons name="call" size={22} color="#87CEEB" />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.modernVideoButton}
          onPress={(e) => {
            e.stopPropagation();
            const contact = contacts.find(c => c.id === item.contactId);
            if (contact) {
              handleVideoCall(contact);
            }
          }}
        >
          <Ionicons name="videocam" size={22} color="#87CEEB" />
        </TouchableOpacity>
      </View>
    </ResponsiveListCard>
  );

  // Render contact item
  const renderContactItem = ({ item }: { item: IraChatContact }) => (
    <ResponsiveCard style={styles.contactItem}>
      <Image
        source={{
          uri: item.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(item.name)}&background=667eea&color=fff`
        }}
        style={styles.avatar}
      />
      
      <View style={styles.contactInfo}>
        <Text style={styles.contactName}>{item.name}</Text>
        <Text style={styles.phoneNumber}>{item.phoneNumber}</Text>
        {item.isIraChatUser && (
          <Text style={styles.iraChatLabel}>IraChat User</Text>
        )}
      </View>
      
      <View style={styles.contactActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleVoiceCall(item)}
        >
          <Ionicons name="call" size={20} color="#667eea" />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleVideoCall(item)}
        >
          <Ionicons name="videocam" size={20} color="#667eea" />
        </TouchableOpacity>
      </View>
    </ResponsiveCard>
  );

  // Render empty state
  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons 
        name={activeTab === 'history' ? 'call-outline' : 'people-outline'} 
        size={64} 
        color="#E5E7EB" 
      />
      <Text style={styles.emptyTitle}>
        {activeTab === 'history' ? 'No Call History' : 'No Contacts'}
      </Text>
      <Text style={styles.emptySubtitle}>
        {activeTab === 'history' 
          ? 'Your call history will appear here'
          : 'Add contacts to start making calls'
        }
      </Text>
    </View>
  );

  return (
    <ResponsiveContainer paddingHorizontal={false} paddingVertical={false}>
      {/* Header with Inline Search */}
      <View style={styles.headerContainer}>
        <View style={styles.headerContent}>
          {!showSearch ? (
            <>
              <Text style={styles.headerTitle}>Calls</Text>
              <TouchableOpacity
                style={styles.searchButton}
                onPress={() => setShowSearch(true)}
              >
                <Ionicons name="search" size={24} color="#FFFFFF" />
              </TouchableOpacity>
            </>
          ) : (
            <>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => {
                  setShowSearch(false);
                  setSearchQuery('');
                }}
              >
                <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
              </TouchableOpacity>
              <View style={styles.inlineSearchContainer}>
                <Ionicons
                  name="search"
                  size={20}
                  color="#D1D5DB"
                  style={styles.searchIcon}
                />
                <TextInput
                  style={styles.inlineSearchInput}
                  placeholder={`Search ${activeTab === 'history' ? 'call history' : 'contacts'}...`}
                  placeholderTextColor="#9CA3AF"
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                  autoFocus
                />
                {searchQuery.length > 0 && (
                  <TouchableOpacity
                    onPress={() => {
                      setSearchQuery('');
                      // Immediate clear - bypass debounce
                      performSearch('');
                    }}
                    style={styles.clearButton}
                  >
                    <Ionicons name="close" size={20} color="#374151" />
                  </TouchableOpacity>
                )}
              </View>
            </>
          )}
        </View>
      </View>



      {/* Modern Tab Selector with Animated Indicator */}
      <View style={styles.modernTabContainer}>
        <View style={styles.tabsWrapper}>
          <TouchableOpacity
            style={styles.modernTab}
            onPress={() => switchToTab('history')}
          >
            <Text style={[
              styles.modernTabText,
              { fontSize: responsiveStyles.fontScale },
              activeTab === 'history' && styles.modernActiveTabText
            ]}>
              Recent
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.modernTab}
            onPress={() => switchToTab('contacts')}
          >
            <Text style={[
              styles.modernTabText,
              { fontSize: responsiveStyles.fontScale },
              activeTab === 'contacts' && styles.modernActiveTabText
            ]}>
              Contacts
            </Text>
          </TouchableOpacity>
        </View>

        {/* Animated Tab Indicator */}
        <Animated.View
          style={[
            styles.tabIndicator,
            animatedIndicatorStyle,
          ]}
        />
      </View>

      {/* Swipeable Content */}
      <GestureHandlerRootView style={styles.gestureContainer}>
        <GestureDetector gesture={Gesture.Simultaneous(panGesture)}>
          <Animated.View style={[styles.contentWrapper, animatedContentStyle]}>
            {isLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#667eea" />
                <Text style={styles.loadingText}>Loading...</Text>
              </View>
            ) : (
              activeTab === 'history' ? (
                <FlatList<CallLog>
                  ref={componentRef}
                  data={callHistory}
                  renderItem={renderCallHistoryItem}
                  keyExtractor={(item) => item.id}
                  style={styles.list}
                  contentContainerStyle={styles.listContent}
                  showsVerticalScrollIndicator={false}
                  scrollEventThrottle={16}
                  refreshControl={
                    <RefreshControl
                      refreshing={isRefreshing}
                      onRefresh={handleRefresh}
                      colors={['#87CEEB']}
                    />
                  }
                  ListEmptyComponent={renderEmptyState}
                />
              ) : (
                <FlatList<IraChatContact>
                  ref={activeTab === 'contacts' ? componentRef : null}
                  data={filteredContacts}
                  renderItem={renderContactItem}
                  keyExtractor={(item) => item.id}
                  style={styles.list}
                  contentContainerStyle={styles.listContent}
                  showsVerticalScrollIndicator={false}
                  scrollEventThrottle={16}
                  refreshControl={
                    <RefreshControl
                      refreshing={isRefreshing}
                      onRefresh={handleRefresh}
                      colors={['#87CEEB']}
                    />
                  }
                  ListEmptyComponent={renderEmptyState}
                />
              )
            )}
          </Animated.View>
        </GestureDetector>
      </GestureHandlerRootView>

      {/* Floating Action Button for Quick Call Actions */}
      <FloatingActionButton
        actions={[
          QuickNavActions.contacts,
          {
            icon: 'call-outline',
            label: 'Voice Call',
            onPress: () => navigationService.openContacts(),
            color: '#10B981',
          },
          {
            icon: 'videocam-outline',
            label: 'Video Call',
            onPress: () => navigationService.openContacts(),
            color: '#3B82F6',
          },
        ]}
        mainAction={{
          icon: 'add-outline',
          onPress: () => navigationService.openContacts(),
          backgroundColor: '#667eea',
        }}
      />
    </ResponsiveContainer>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(18, 18, 18, 0.95)', // Match chats page conversations area background
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#374151',
  },
  // New Header Styles
  headerContainer: {
    backgroundColor: '#374151',
    paddingTop: 50,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#4B5563',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    height: 44,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  inlineSearchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4B5563',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginLeft: 12,
    gap: 8,
    borderWidth: 1,
    borderColor: '#6B7280',
    height: 40,
  },
  inlineSearchInput: {
    flex: 1,
    fontSize: 16,
    color: '#FFFFFF',
    paddingVertical: 0,
    paddingHorizontal: 0,
    textAlignVertical: 'center',
    includeFontPadding: false,
    height: 24,
  },
  searchIcon: {
    marginRight: 4,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerSearchContainer: {
    paddingHorizontal: 16,
    paddingTop: 12,
    paddingBottom: 4,
  },
  headerSearchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4B5563',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 8,
    borderWidth: 1,
    borderColor: '#6B7280',
    height: 40,
  },
  headerSearchIcon: {
    marginRight: 4,
  },
  headerSearchInput: {
    flex: 1,
    fontSize: 16,
    color: '#FFFFFF',
    paddingVertical: 0,
    paddingHorizontal: 0,
    textAlignVertical: 'center',
    includeFontPadding: false,
    height: 24,
  },
  clearButton: {
    padding: 4,
    borderRadius: 12,
    backgroundColor: '#9CA3AF',
    justifyContent: 'center',
    alignItems: 'center',
    width: 28,
    height: 28,
    marginLeft: 4,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 20,
    paddingHorizontal: 12,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 10,
    fontSize: 16,
    color: '#374151',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#4B5563',
    borderBottomWidth: 1,
    borderBottomColor: '#374151',
  },
  tab: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#FFFFFF',
  },
  tabText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E0E7FF',
  },
  activeTabText: {
    color: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6B7280',
  },
  list: {
    flex: 1,
  },
  listContent: {
    paddingVertical: 8,
  },
  // IRACHAT MODERN CALL ITEMS
  modernCallItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 14,
    backgroundColor: 'rgba(18, 18, 18, 0.95)', // Match chats page conversations area background
    borderBottomWidth: 0.5,
    borderBottomColor: '#E1E5E9',
    marginHorizontal: 0,
  },
  modernAvatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  modernAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  callTypeIndicator: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  modernCallInfo: {
    flex: 1,
    marginRight: 12,
  },
  modernCallHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  modernContactName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    flex: 1,
  },
  modernCallTime: {
    fontSize: 13,
    color: '#8E8E93',
    fontWeight: '400',
  },
  modernCallDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  modernCallStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modernCallIcon: {
    marginRight: 4,
  },
  modernCallStatusText: {
    fontSize: 14,
    color: '#8E8E93',
    fontWeight: '400',
  },
  modernDuration: {
    fontSize: 13,
    color: '#8E8E93',
    fontWeight: '400',
  },
  modernCallActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modernCallButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  modernVideoButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  callItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'rgba(18, 18, 18, 0.95)', // Match chats page conversations area background
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'rgba(18, 18, 18, 0.95)', // Match chats page conversations area background
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  callInfo: {
    flex: 1,
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 4,
  },
  phoneNumber: {
    fontSize: 14,
    color: '#6B7280',
  },
  iraChatLabel: {
    fontSize: 12,
    color: '#667eea',
    fontWeight: '500',
    marginTop: 2,
  },
  callDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  callIcon: {
    marginRight: 4,
  },
  callTime: {
    fontSize: 14,
    color: '#6B7280',
  },
  callActions: {
    alignItems: 'flex-end',
  },
  contactActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  duration: {
    fontSize: 12,
    color: '#6B7280',
    marginBottom: 4,
  },
  callButton: {
    padding: 8,
  },
  actionButton: {
    padding: 8,
    marginLeft: 8,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 64,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#374151',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },

  // Modern Tab Styles
  modernTabContainer: {
    backgroundColor: '#1F2937',
    paddingHorizontal: 16,
    position: 'relative',
    height: 36, // Reduced from 48 to match chats page
  },
  tabsWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    height: 36, // Reduced from 48 to match chats page
  },
  modernTab: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 6, // Reduced from 12 to match chats page
    height: 36, // Added fixed height to match chats page
  },
  modernTabText: {
    fontSize: 14, // Reduced from 16 to match chats page
    fontWeight: '500', // Reduced from '600' to match chats page
    color: '#9CA3AF',
    textAlign: 'center',
  },
  modernActiveTabText: {
    color: '#667eea',
    fontWeight: '600', // Added semibold weight for active state to match chats page
  },
  tabIndicator: {
    position: 'absolute',
    bottom: 0,
    height: 2, // Reduced from 3 to match chats page
    width: '50%',
    backgroundColor: '#667eea',
    borderRadius: 1, // Reduced from 2 to match chats page
  },

  // Gesture and Animation Styles
  gestureContainer: {
    flex: 1,
  },
  contentWrapper: {
    flex: 1,
  },
});
