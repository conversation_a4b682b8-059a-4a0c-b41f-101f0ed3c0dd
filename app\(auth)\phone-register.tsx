// Phone-Based Registration Screen for IraChat with International Support
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useRef, useState, useCallback, useEffect } from 'react';
import { navigationService } from '../../src/services/navigationService';
import {
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  StyleSheet,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { securePhoneAuth } from '../../src/services/securePhoneAuth';
import { PhoneValidator } from '../../src/utils/inputValidation';
import { handleAuthError, handleValidationError } from '../../src/services/errorHandling';
import PhoneNumberInput from '../../src/components/ui/PhoneNumberInput';

// Phone number validation utility
const validatePhoneNumber = (phoneNumber: string, countryCode: string): boolean => {
  const cleaned = phoneNumber.replace(/\D/g, '');
  
  // Basic validation based on country code
  switch (countryCode) {
    case '+1': // US/Canada
      return cleaned.length === 10;
    case '+44': // UK
      return cleaned.length >= 10 && cleaned.length <= 11;
    case '+33': // France
      return cleaned.length === 9;
    case '+49': // Germany
      return cleaned.length >= 10 && cleaned.length <= 12;
    case '+91': // India
      return cleaned.length === 10;
    case '+86': // China
      return cleaned.length === 11;
    case '+81': // Japan
      return cleaned.length >= 10 && cleaned.length <= 11;
    case '+234': // Nigeria
      return cleaned.length === 10;
    case '+254': // Kenya
    case '+256': // Uganda
    case '+255': // Tanzania
    case '+250': // Rwanda
      return cleaned.length === 9;
    case '+971': // UAE
      return cleaned.length === 9;
    case '+966': // Saudi Arabia
      return cleaned.length === 9;
    case '+20': // Egypt
      return cleaned.length >= 10 && cleaned.length <= 11;
    case '+90': // Turkey
      return cleaned.length === 10;
    default:
      // Generic validation for other countries (6-15 digits)
      return cleaned.length >= 6 && cleaned.length <= 15;
  }
};

export default function PhoneRegisterScreen() {
  const router = useRouter();
  const [step, setStep] = useState<'phone' | 'verification'>('phone');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [countryCode, setCountryCode] = useState('+256'); // Default to Uganda
  const [verificationCode, setVerificationCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [phoneError, setPhoneError] = useState('');

  // Refs for auto-focus
  const codeInputRef = useRef<TextInput>(null);

  // Clear phone number when country changes (UX improvement)
  useEffect(() => {
    setPhoneNumber('');
    setPhoneError('');
  }, [countryCode]);

  // Validate phone number and show error message
  useEffect(() => {
    if (phoneNumber.length > 0 && !validatePhoneNumber(phoneNumber, countryCode)) {
      setPhoneError(`Please enter a valid number for ${countryCode}`);
    } else {
      setPhoneError('');
    }
  }, [phoneNumber, countryCode]);

  // Cleanup Firebase reCAPTCHA on unmount
  useEffect(() => {
    return () => {
      // Clean up Firebase reCAPTCHA
      securePhoneAuth.cleanup();

      // Additional cleanup for web platform
      if (Platform.OS === 'web') {
        try {
          const globalWindow = global as any;
          const recaptchaVerifier = globalWindow.recaptchaVerifier;
          if (recaptchaVerifier && typeof recaptchaVerifier.clear === 'function') {
            recaptchaVerifier.clear();
          }
        } catch (e) {
          // Ignore errors during cleanup
        }
      }
    };
  }, []);

  // Fallback navigation using router directly
  const navigateWithRouter = useCallback((route: string) => {
    try {
      router.push(route as any);
    } catch (error) {
      console.error('Router navigation error:', error);
      // Fallback to navigationService
      navigationService.navigate(route);
    }
  }, [router]);

  // Send verification code with secure validation (memoized for performance)
  const sendVerificationCode = useCallback(async () => {
    setIsLoading(true);

    try {
      // Format phone number for validation
      const cleanPhone = phoneNumber.replace(/\D/g, '');
      const formattedPhone = `${countryCode}${cleanPhone}`;

      // Validate phone number using secure validator
      const validation = PhoneValidator.validate(formattedPhone);
      if (!validation.isValid) {
        const errorMessage = await handleValidationError(
          new Error(validation.error || 'Invalid phone number'),
          { phoneNumber: formattedPhone, countryCode }
        );
        Alert.alert('Invalid Phone Number', errorMessage);
        return;
      }

      // Use secure phone auth service
      const result = await securePhoneAuth.sendVerificationCode(validation.sanitized!);

      if (result.success) {
        setStep('verification');
        startCountdown();
        // Add haptic feedback for success
        if (Platform.OS !== 'web') {
          try {
            const Haptics = require('expo-haptics');
            await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          } catch (e) {
            // Haptics not available, continue silently
          }
        }
        setTimeout(() => {
          codeInputRef.current?.focus();
        }, 500);
      } else {
        Alert.alert('Error', result.error || 'Failed to send verification code');
      }

      if (result.success) {
        setStep('verification');
        startCountdown();
        // Add haptic feedback for success
        if (Platform.OS !== 'web') {
          try {
            const Haptics = require('expo-haptics');
            await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          } catch (e) {
            // Haptics not available, continue silently
          }
        }
        setTimeout(() => {
          codeInputRef.current?.focus();
        }, 500);
      } else {
        Alert.alert('Error', result.error || 'Failed to send verification code');
      }
    } catch (error: any) {
      const errorMessage = await handleAuthError(error, {
        action: 'sendVerificationCode',
        phoneNumber: `${countryCode}${phoneNumber}`,
      });
      Alert.alert('Error', errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [phoneNumber, countryCode]);

  // Verify SMS code with secure validation (memoized for performance)
  const verifyCode = useCallback(async () => {
    if (verificationCode.length !== 6) {
      const errorMessage = await handleValidationError(
        new Error('Invalid verification code length'),
        { codeLength: verificationCode.length }
      );
      Alert.alert('Error', errorMessage);
      return;
    }

    setIsLoading(true);

    try {
      // Use secure phone auth service
      const result = await securePhoneAuth.verifyCode(verificationCode);

      if (result.success) {
        // Add haptic feedback for success
        if (Platform.OS !== 'web') {
          try {
            const Haptics = require('expo-haptics');
            await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          } catch (e) {
            // Haptics not available, continue silently
          }
        }

        Alert.alert(
          'Phone Verified!',
          'Your phone number has been verified successfully. Please complete your profile.',
          [
            {
              text: 'Continue',
              onPress: () => {
                // Navigate to profile completion with verified phone (correct route)
                try {
                  router.push({
                    pathname: '/(auth)/register',
                    params: {
                      verifiedPhone: `${countryCode}${phoneNumber}`,
                      phoneVerified: 'true'
                    }
                  });
                } catch (error) {
                  console.error('Navigation error:', error);
                  // Fallback navigation to auth register
                  router.push('/(auth)/register');
                }
              },
            },
          ]
        );
      } else {
        // Add haptic feedback for error
        if (Platform.OS !== 'web') {
          try {
            const Haptics = require('expo-haptics');
            await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
          } catch (e) {
            // Haptics not available, continue silently
          }
        }

        Alert.alert('Error', result.error || 'Invalid verification code');
      }
    } catch (error: any) {
      const errorMessage = await handleAuthError(error, {
        action: 'verifyCode',
        codeLength: verificationCode.length,
      });
      Alert.alert('Error', errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [verificationCode, navigateWithRouter]);

  // Start countdown for resend
  const startCountdown = useCallback(() => {
    setCountdown(60);
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  }, []);

  // Resend verification code with auto-focus
  const resendCode = useCallback(async () => {
    if (countdown > 0) return;

    setVerificationCode('');
    await sendVerificationCode();

    // Auto-focus on input after resend (UX improvement)
    setTimeout(() => {
      codeInputRef.current?.focus();
    }, 500);
  }, [countdown, sendVerificationCode]);

  // Format display phone number
  const getDisplayPhoneNumber = () => {
    const cleanPhone = phoneNumber.replace(/\D/g, '');
    return `${countryCode} ${cleanPhone}`;
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" backgroundColor="#87CEEB" />
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigationService.goBack()} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>
            {step === 'phone' ? 'Enter Phone Number' : 'Verify Phone Number'}
          </Text>
        </View>

        <View style={styles.content}>
          {/* Logo */}
          <View style={styles.logoContainer}>
            <View style={styles.logo}>
              <Text style={styles.logoText}>
                I
              </Text>
            </View>
            <Text style={styles.appName}>
              IraChat
            </Text>
          </View>

          {step === 'phone' ? (
            // Phone Number Step
            <>
              <Text style={styles.description}>
                Enter your phone number to get started.{'\n'}
                We&apos;ll send you a verification code.
              </Text>

              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>
                  Phone Number
                </Text>
                <PhoneNumberInput
                  value={phoneNumber}
                  onChangeText={setPhoneNumber}
                  onCountryChange={setCountryCode}
                  placeholder="Enter phone number"
                  editable={!isLoading}
                  error={!!phoneError}
                />

                {/* Show validation error message */}
                {phoneError ? (
                  <Text style={styles.errorText}>
                    {phoneError}
                  </Text>
                ) : null}
              </View>

              <TouchableOpacity
                onPress={sendVerificationCode}
                disabled={isLoading || !validatePhoneNumber(phoneNumber, countryCode)}
                style={[
                  styles.button,
                  validatePhoneNumber(phoneNumber, countryCode) ? styles.buttonEnabled : styles.buttonDisabled
                ]}
              >
                {isLoading ? (
                  <ActivityIndicator size="small" color="white" />
                ) : (
                  <Text style={styles.buttonText}>
                    Send Verification Code
                  </Text>
                )}
              </TouchableOpacity>
            </>
          ) : (
            // Verification Code Step
            <>
              <Text style={styles.description}>
                Enter the 6-digit code sent to{'\n'}
                <Text style={{ fontWeight: '600' }}>{getDisplayPhoneNumber()}</Text>
              </Text>

              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>
                  Verification Code
                </Text>
                <TextInput
                  ref={codeInputRef}
                  value={verificationCode}
                  onChangeText={setVerificationCode}
                  placeholder="123456"
                  keyboardType="number-pad"
                  maxLength={6}
                  style={styles.codeInput}
                />
              </View>

              <TouchableOpacity
                onPress={verifyCode}
                disabled={isLoading || verificationCode.length !== 6}
                style={[
                  styles.button,
                  verificationCode.length === 6 ? styles.buttonEnabled : styles.buttonDisabled
                ]}
              >
                {isLoading ? (
                  <ActivityIndicator size="small" color="white" />
                ) : (
                  <Text style={styles.buttonText}>
                    Verify & Continue
                  </Text>
                )}
              </TouchableOpacity>

              {/* Resend Code */}
              <TouchableOpacity
                onPress={resendCode}
                disabled={countdown > 0}
                style={styles.resendButton}
              >
                <Text style={[
                  styles.resendText,
                  countdown > 0 ? styles.resendDisabled : styles.resendEnabled
                ]}>
                  {countdown > 0 ? `Resend code in ${countdown}s` : 'Resend verification code'}
                </Text>
              </TouchableOpacity>
            </>
          )}
        </View>

        {/* reCAPTCHA container (invisible) */}
        <View id="recaptcha-container" style={{ height: 0, width: 0 }} />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#87CEEB',
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
    marginRight: 32, // Compensate for back button
  },
  content: {
    flex: 1,
    padding: 24,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logo: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#87CEEB',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  logoText: {
    fontSize: 32,
    color: 'white',
    fontWeight: 'bold',
  },
  appName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  description: {
    fontSize: 16,
    color: '#374151',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
  },
  inputContainer: {
    marginBottom: 32,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  button: {
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 16,
  },
  buttonEnabled: {
    backgroundColor: '#87CEEB',
  },
  buttonDisabled: {
    backgroundColor: '#d1d5db',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  resendButton: {
    alignItems: 'center',
  },
  resendText: {
    fontSize: 14,
    fontWeight: '600',
  },
  resendEnabled: {
    color: '#87CEEB',
  },
  resendDisabled: {
    color: '#9ca3af',
  },
  codeInput: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    padding: 16,
    fontSize: 18,
    textAlign: 'center',
    letterSpacing: 4,
    backgroundColor: 'white',
  },
  errorText: {
    color: '#EF4444',
    fontSize: 12,
    marginTop: 4,
    marginLeft: 4,
  },
});
