import { Redirect } from "expo-router";
import { useEffect, useState } from "react";
import { ActivityIndicator, Platform, Text, View, BackHandler } from "react-native";
import { useAuthPersistence } from "../src/hooks/useAuthPersistence";
import {
    isFirstLaunch,
    markAppLaunched,
} from "../src/services/authStorageSimple";

export default function Index() {
  const [isLoading, setIsLoading] = useState(true);
  const [redirectTo, setRedirectTo] = useState<string | null>(null);
  const [authTimeout, setAuthTimeout] = useState(false);

  const { isInitializing, isAuthenticated } = useAuthPersistence();

  useEffect(() => {
    const determineRoute = async () => {
      console.log('📱 Index: Determining route - authTimeout:', authTimeout, 'isInitializing:', isInitializing, 'isAuthenticated:', isAuthenticated);

      if (authTimeout) {
        console.log('📱 Index: Auth timeout, redirecting to welcome');
        setRedirectTo("/(auth)/welcome");
        setIsLoading(false);
        return;
      }

      if (isInitializing && !authTimeout) {
        console.log('📱 Index: Still initializing, waiting...');
        return;
      }

      try {
        if (isAuthenticated) {
          console.log('📱 Index: User authenticated, redirecting to tabs');
          setRedirectTo("/(tabs)");
        } else {
          console.log('📱 Index: User not authenticated, checking first launch');
          const isFirstTime = await isFirstLaunch();

          if (isFirstTime) {
            console.log('📱 Index: First time user, marking app launched');
            await markAppLaunched();
            setRedirectTo("/(auth)/welcome");
          } else {
            console.log('📱 Index: Returning user, redirecting to welcome');
            setRedirectTo("/(auth)/welcome");
          }
        }
      } catch (error) {
        console.error('📱 Index: Error determining route:', error);
        setRedirectTo("/(auth)/welcome");
      } finally {
        setIsLoading(false);
      }
    };

    determineRoute();
  }, [isInitializing, isAuthenticated, authTimeout]);

  // Add aggressive timeout for auth initialization - reduced for faster UX
  useEffect(() => {
    const authInitTimeout = setTimeout(() => {
      if (isInitializing) {

        setAuthTimeout(true);

        setRedirectTo("/(auth)/welcome");
        setIsLoading(false);
      }
    }, 1500); // 1.5 second timeout for auth (reduced for faster registration flow)

    return () => clearTimeout(authInitTimeout);
  }, [isInitializing]);

  // Add general loading timeout fallback - reduced for faster UX
  useEffect(() => {
    const loadingTimeout = setTimeout(() => {
      if (isLoading && !redirectTo) {
        console.log('📱 Index: Loading timeout reached, redirecting to welcome');
        setRedirectTo("/(auth)/welcome");
        setIsLoading(false);
      }
    }, 3000); // 3 second timeout (reduced from 8 seconds for faster registration flow)

    return () => clearTimeout(loadingTimeout);
  }, [isLoading, redirectTo]);

  // Handle back button to prevent infinite loading loops
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      console.log('📱 Index: Back button pressed during loading');

      if (isLoading) {
        // If we're loading, force redirect to welcome to break any loops
        console.log('📱 Index: Breaking loading loop, redirecting to welcome');
        setRedirectTo("/(auth)/welcome");
        setIsLoading(false);
        return true; // Prevent default back action
      }

      return false; // Allow default back action
    });

    return () => backHandler.remove();
  }, [isLoading]);

  // Show loading screen while determining route
  if (isLoading || isInitializing) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: "#667eea",
        }}
      >
        <ActivityIndicator size="large" color="#FFFFFF" />
        <Text
          style={{
            color: "#FFFFFF",
            fontSize: 18,
            fontWeight: "600",
            marginTop: 16,
          }}
        >
          Loading IraChat...
        </Text>
       
      </View>
    );
  }

  // Redirect to determined route
  if (redirectTo) {
    console.log('📱 Index: Redirecting to:', redirectTo);
    return <Redirect href={redirectTo as any} />;
  }

  // Fallback - Force redirect to welcome if nothing else works
  console.log('📱 Index: No redirect determined, using fallback to welcome');
  return <Redirect href="/(auth)/welcome" />;
}
