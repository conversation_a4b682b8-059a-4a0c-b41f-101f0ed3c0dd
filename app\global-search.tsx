import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { collection, getDocs, limit, orderBy, query, where } from 'firebase/firestore';
import { useCallback, useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    FlatList,
    Image,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
    Dimensions,
} from 'react-native';
import { GestureHandlerRootView, PanGestureHandler, State } from 'react-native-gesture-handler';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';
import NetInfo from '@react-native-community/netinfo';
import { db, auth } from '../src/services/firebaseSimple';
import { formatTimeAgo } from '../src/utils/dateUtils';
import { navigationService } from '../src/services/navigationService';
import { FloatingActionButton, QuickNavActions } from '../src/components/NavigationHelper';
import { localUpdatesStorage } from '../src/services/localUpdatesStorage';

interface SearchResult {
  id: string;
  type: 'chat' | 'contact' | 'message' | 'group' | 'update';
  title: string;
  subtitle: string;
  content?: string;
  avatar?: string;
  timestamp?: string;
}

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default function GlobalSearchScreen() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeFilter, setActiveFilter] = useState<string>('all');
  const [isOnline, setIsOnline] = useState(true);

  // Swipe navigation
  const translateX = useSharedValue(0);
  const currentFilterIndex = useSharedValue(0);

  const filters = [
    { key: 'all', label: 'All', icon: 'search' },
    { key: 'chats', label: 'Chats', icon: 'chatbubbles' },
    { key: 'contacts', label: 'Contacts', icon: 'people' },
    { key: 'messages', label: 'Messages', icon: 'chatbubble' },
    { key: 'groups', label: 'Groups', icon: 'people-circle' },
    { key: 'updates', label: 'Updates', icon: 'camera' },
  ];

  // Network monitoring
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsOnline(state.isConnected ?? false);
    });

    return unsubscribe;
  }, []);

  // Swipe navigation functions
  const switchToFilter = (filterKey: string) => {
    setActiveFilter(filterKey);
    const index = filters.findIndex(f => f.key === filterKey);
    currentFilterIndex.value = index;
  };

  const handleSwipeLeft = () => {
    const nextIndex = Math.min(currentFilterIndex.value + 1, filters.length - 1);
    if (nextIndex !== currentFilterIndex.value) {
      switchToFilter(filters[nextIndex].key);
    }
  };

  const handleSwipeRight = () => {
    const prevIndex = Math.max(currentFilterIndex.value - 1, 0);
    if (prevIndex !== currentFilterIndex.value) {
      switchToFilter(filters[prevIndex].key);
    }
  };

  const onGestureEvent = (event: any) => {
    translateX.value = event.nativeEvent.translationX;
  };

  const onHandlerStateChange = (event: any) => {
    if (event.nativeEvent.state === State.END) {
      const { translationX, velocityX } = event.nativeEvent;

      if (Math.abs(translationX) > SCREEN_WIDTH * 0.25 || Math.abs(velocityX) > 500) {
        if (translationX > 0) {
          runOnJS(handleSwipeRight)();
        } else {
          runOnJS(handleSwipeLeft)();
        }
      }

      translateX.value = withSpring(0);
    }
  };

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: translateX.value * 0.1 }], // Subtle movement
    };
  });

  const performSearch = useCallback(async () => {
    if (!searchQuery.trim()) return;

    setIsLoading(true);
    try {
      const searchResults: SearchResult[] = [];

      if (isOnline) {
        // Online search - full Firebase search
        try {
          // Search chats
          if (activeFilter === 'all' || activeFilter === 'chats') {
            const chatsResults = await searchChats(searchQuery);
            searchResults.push(...chatsResults);
          }

          // Search contacts
          if (activeFilter === 'all' || activeFilter === 'contacts') {
            const contactsResults = await searchContacts(searchQuery);
            searchResults.push(...contactsResults);
          }

          // Search messages
          if (activeFilter === 'all' || activeFilter === 'messages') {
            const messagesResults = await searchMessages(searchQuery);
            searchResults.push(...messagesResults);
          }

          // Search groups
          if (activeFilter === 'all' || activeFilter === 'groups') {
            const groupsResults = await searchGroups(searchQuery);
            searchResults.push(...groupsResults);
          }

          // Search updates
          if (activeFilter === 'all' || activeFilter === 'updates') {
            const updatesResults = await searchUpdates(searchQuery);
            searchResults.push(...updatesResults);
          }
        } catch (onlineError) {
          console.warn('Online search failed, falling back to offline search:', onlineError);
          // Fallback to offline search if online search fails
          await performOfflineSearch(searchQuery, searchResults);
        }
      } else {
        // Offline search - search only in locally cached data
        await performOfflineSearch(searchQuery, searchResults);
      }

      setResults(searchResults);
    } catch (error) {
      console.error('Search error:', error);
      Alert.alert(
        'Search Error',
        isOnline
          ? 'Failed to search. Please try again.'
          : 'Search failed. Limited to cached data while offline.'
      );
    } finally {
      setIsLoading(false);
    }
  }, [searchQuery, activeFilter, isOnline]);

  useEffect(() => {
    if (searchQuery.trim().length > 2) {
      performSearch();
    } else {
      setResults([]);
    }
  }, [searchQuery, activeFilter, performSearch]);

  // Offline search function - searches locally cached data
  const performOfflineSearch = async (query: string, searchResults: SearchResult[]) => {
    try {
      // Search updates from local storage
      if (activeFilter === 'all' || activeFilter === 'updates') {
        const localUpdates = await localUpdatesStorage.getUpdates(50, 0, false);
        const filteredUpdates = localUpdates.filter(update => {
          const searchText = query.toLowerCase();
          const matchesCaption = update.caption?.toLowerCase().includes(searchText);
          const matchesUsername = update.userName.toLowerCase().includes(searchText);
          return matchesCaption || matchesUsername;
        });

        const updatesResults: SearchResult[] = filteredUpdates.map(update => ({
          id: update.id,
          type: 'update',
          title: update.userName,
          subtitle: update.caption || 'No caption',
          content: formatTimeAgo(update.timestamp),
          avatar: update.userAvatar,
        }));

        searchResults.push(...updatesResults);
      }

      // Note: For offline mode, we can only search cached updates
      // Other data types (chats, contacts, messages, groups) would need
      // their own local storage implementations to be searchable offline
      if (activeFilter === 'chats' || activeFilter === 'contacts' ||
          activeFilter === 'messages' || activeFilter === 'groups') {
        // Show a message that these searches require internet
        console.log(`Offline search for ${activeFilter} not available - requires internet connection`);
      }
    } catch (error) {
      console.error('Offline search error:', error);
    }
  };

  const searchChats = async (searchQuery: string): Promise<SearchResult[]> => {
    try {
      if (!db) {
        console.warn('Database not available for chat search');
        return [];
      }

      if (!auth?.currentUser) {
        console.warn('User not authenticated for chat search');
        return [];
      }

      const chatsRef = collection(db, 'chats');
      const q = query(chatsRef, limit(50));

      const snapshot = await getDocs(q);
      return snapshot.docs
        .filter(doc => {
          const data = doc.data();
          const name = data.name || '';
          return name.toLowerCase().includes(searchQuery.toLowerCase());
        })
        .slice(0, 10)
        .map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            type: 'chat' as const,
            title: data.name || 'Unknown Chat',
            subtitle: `${data.participants?.length || 0} participants`,
            avatar: data.avatar,
          };
        });
    } catch (error) {
      console.error('Error searching chats:', error);
      return [];
    }
  };

  const searchContacts = async (searchQuery: string): Promise<SearchResult[]> => {
    try {
      if (!db) {
        console.warn('Database not available for contact search');
        return [];
      }

      if (!auth?.currentUser) {
        console.warn('User not authenticated for contact search');
        return [];
      }

      const usersRef = collection(db, 'users');
      const q = query(usersRef, limit(50));

      const snapshot = await getDocs(q);
      return snapshot.docs
        .filter(doc => {
          const data = doc.data();
          const name = data.name || '';
          const username = data.username || '';
          return name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                 username.toLowerCase().includes(searchQuery.toLowerCase());
        })
        .slice(0, 10)
        .map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            type: 'contact' as const,
            title: data.name || 'Unknown User',
            subtitle: data.username ? `@${data.username}` : data.phoneNumber || data.email || '',
            avatar: data.avatar,
          };
        });
    } catch (error) {
      console.error('Error searching contacts:', error);
      return [];
    }
  };

  const searchMessages = async (searchQuery: string): Promise<SearchResult[]> => {
    try {
      if (!db) {
        console.warn('Database not available for message search');
        return [];
      }

      if (!auth?.currentUser) {
        console.warn('User not authenticated for message search');
        return [];
      }

      console.log('🔍 Searching messages for:', searchQuery);

      // Search across all messages in Firebase
      const messagesRef = collection(db, 'messages');
      const q = query(
        messagesRef,
        where('content', '>=', searchQuery),
        where('content', '<=', searchQuery + '\uf8ff'),
        orderBy('content'),
        orderBy('timestamp', 'desc'),
        limit(10)
      );

      const querySnapshot = await getDocs(q);
      const results: SearchResult[] = [];

      querySnapshot.forEach((doc) => {
        const messageData = doc.data();
        results.push({
          id: doc.id,
          type: 'message',
          title: messageData.content.substring(0, 50) + (messageData.content.length > 50 ? '...' : ''),
          subtitle: `From ${messageData.senderName} • ${formatTimeAgo(messageData.timestamp?.toDate() || new Date())}`,
          content: messageData.content,
        });
      });

      console.log('✅ Found', results.length, 'message results');
      return results;
    } catch (error) {
      console.error('❌ Error searching messages:', error);
      // Return empty array instead of mock data on error
      return [];
    }
  };

  const searchGroups = async (searchQuery: string): Promise<SearchResult[]> => {
    try {
      if (!db) {
        console.warn('Database not available for group search');
        return [];
      }

      if (!auth?.currentUser) {
        console.warn('User not authenticated for group search');
        return [];
      }

      const groupsRef = collection(db, 'groups');
      const q = query(groupsRef, limit(50));

      const snapshot = await getDocs(q);
      return snapshot.docs
        .filter(doc => {
          const data = doc.data();
          const name = data.name || '';
          return name.toLowerCase().includes(searchQuery.toLowerCase());
        })
        .slice(0, 10)
        .map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            type: 'group' as const,
            title: data.name || 'Unknown Group',
            subtitle: `${data.memberCount || 0} members`,
            avatar: data.avatar,
          };
        });
    } catch (error) {
      console.error('Error searching groups:', error);
      return [];
    }
  };

  const searchUpdates = async (searchQuery: string): Promise<SearchResult[]> => {
    try {
      if (!db) {
        console.warn('Database not available for updates search');
        return [];
      }

      if (!auth?.currentUser) {
        console.warn('User not authenticated for updates search');
        return [];
      }

      const updatesRef = collection(db, 'updates');
      const q = query(
        updatesRef,
        orderBy('timestamp', 'desc'),
        limit(10)
      );
      
      const snapshot = await getDocs(q);
      return snapshot.docs
        .filter(doc => {
          const data = doc.data();
          const caption = data.caption || '';
          return caption.toLowerCase().includes(searchQuery.toLowerCase());
        })
        .map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            type: 'update' as const,
            title: data.caption || 'Update',
            subtitle: `By ${data.userName || 'Unknown'} • ${formatTime(data.timestamp)}`,
            avatar: data.userAvatar,
          };
        });
    } catch (error) {
      console.error('Error searching updates:', error);
      return [];
    }
  };

  const formatTime = (timestamp: any) => {
    try {
      const date = timestamp?.toDate ? timestamp.toDate() : new Date(timestamp);
      const now = new Date();
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

      if (diffInMinutes < 1) return 'Just now';
      if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
      
      const diffInHours = Math.floor(diffInMinutes / 60);
      if (diffInHours < 24) return `${diffInHours}h ago`;
      
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    } catch {
      return 'Recently';
    }
  };

  const handleResultPress = (result: SearchResult) => {
    switch (result.type) {
      case 'chat':
        router.push(`/chat/${result.id}?name=${result.title}`);
        break;
      case 'contact':
        Alert.alert('Contact', `Open chat with ${result.title}?`, [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Chat', onPress: () => router.push(`/chat/${result.id}?name=${result.title}`) },
        ]);
        break;
      case 'group':
        router.push(`/chat/${result.id}`);
        break;
      case 'update':
        Alert.alert('Update', `View update by ${result.title}?`);
        break;
      case 'message':
        Alert.alert('Message', 'Navigate to message in chat?');
        break;
    }
  };

  const getResultIcon = (type: string) => {
    switch (type) {
      case 'chat': return 'chatbubbles';
      case 'contact': return 'person';
      case 'message': return 'chatbubble';
      case 'group': return 'people-circle';
      case 'update': return 'camera';
      default: return 'search';
    }
  };

  const getResultColor = (type: string) => {
    switch (type) {
      case 'chat': return '#3B82F6';
      case 'contact': return '#10B981';
      case 'message': return '#8B5CF6';
      case 'group': return '#F59E0B';
      case 'update': return '#EF4444';
      default: return '#6B7280';
    }
  };

  const renderResult = ({ item }: { item: SearchResult }) => (
    <TouchableOpacity
      onPress={() => handleResultPress(item)}
      style={styles.searchResultItem}
    >
      <View style={[styles.resultIconContainer, { backgroundColor: `${getResultColor(item.type)}20` }]}>
        {item.avatar ? (
          <Image source={{ uri: item.avatar }} style={styles.resultAvatar} />
        ) : (
          <Ionicons name={getResultIcon(item.type) as any} size={24} color={getResultColor(item.type)} />
        )}
      </View>

      <View style={styles.searchResultContent}>
        <Text style={styles.searchResultUsername}>{item.title}</Text>
        <Text style={styles.searchResultCaption}>{item.subtitle}</Text>
        {item.content && (
          <Text style={styles.searchResultTime} numberOfLines={2}>
            {item.content}
          </Text>
        )}
      </View>

      <View style={styles.resultTypeContainer}>
        <Text style={styles.resultTypeText}>
          {item.type}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <GestureHandlerRootView style={styles.searchContainer}>
      <PanGestureHandler
        onGestureEvent={onGestureEvent}
        onHandlerStateChange={onHandlerStateChange}
      >
        <Animated.View style={[styles.searchContainer, animatedStyle]}>
      {/* Header */}
      <View style={styles.searchHeader}>
        <View style={styles.searchHeaderContent}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#F9FAFB" />
          </TouchableOpacity>
          <View style={styles.searchInputContainer}>
            <Ionicons name="search" size={20} color="#D1D5DB" style={styles.searchIcon} />
            <TextInput
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholder={isOnline ? "Search everything..." : "Search cached data..."}
              style={styles.searchInput}
              placeholderTextColor="#9CA3AF"
              autoFocus
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity
                onPress={() => {
                  setSearchQuery('');
                  setResults([]);
                }}
                style={styles.clearButton}
              >
                <Ionicons name="close" size={20} color="#F9FAFB" />
              </TouchableOpacity>
            )}
          </View>
          <View style={styles.connectionIndicator}>
            <Ionicons
              name={isOnline ? "wifi" : "wifi-outline"}
              size={16}
              color={isOnline ? "#10B981" : "#EF4444"}
            />
          </View>
        </View>
      </View>

      {/* Filters */}
      <View style={styles.filtersContainer}>
        <FlatList
          data={filters}
          horizontal
          showsHorizontalScrollIndicator={false}
          keyExtractor={(item) => item.key}
          renderItem={({ item }) => (
            <TouchableOpacity
              onPress={() => setActiveFilter(item.key)}
              style={[
                styles.filterButton,
                activeFilter === item.key ? styles.activeFilterButton : styles.inactiveFilterButton
              ]}
            >
              <Ionicons
                name={item.icon as any}
                size={16}
                color={activeFilter === item.key ? '#FFFFFF' : '#9CA3AF'}
              />
              <Text
                style={[
                  styles.filterText,
                  activeFilter === item.key ? styles.activeFilterText : styles.inactiveFilterText
                ]}
              >
                {item.label}
              </Text>
            </TouchableOpacity>
          )}
        />
      </View>

      {/* Results */}
      <View style={styles.resultsContainer}>
        {isLoading ? (
          <View style={styles.searchLoadingContainer}>
            <ActivityIndicator size="large" color="#667eea" />
            <Text style={styles.searchLoadingText}>
              {isOnline ? 'Searching...' : 'Searching cached data...'}
            </Text>
          </View>
        ) : results.length > 0 ? (
          <FlatList
            data={results}
            renderItem={renderResult}
            keyExtractor={(item) => `${item.type}-${item.id}`}
            showsVerticalScrollIndicator={false}
          />
        ) : searchQuery.length > 0 ? (
          <View style={styles.searchEmptyContainer}>
            <Ionicons name="search-outline" size={64} color="#E5E7EB" />
            <Text style={styles.searchEmptyText}>No results found</Text>
            <Text style={styles.searchEmptySubtext}>
              {isOnline
                ? 'Try searching with different keywords'
                : 'Limited to cached data while offline'
              }
            </Text>
          </View>
        ) : (
          <View style={styles.searchInitialContainer}>
            <Ionicons name="search" size={64} color="#E5E7EB" />
            <Text style={styles.searchInitialText}>Start searching</Text>
            <Text style={styles.searchInitialSubtext}>
              {isOnline
                ? 'Search across chats, contacts, messages, groups, and updates'
                : 'Search cached updates while offline'
              }
            </Text>
          </View>
        )}
      </View>
        </Animated.View>
      </PanGestureHandler>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  // Search Modal Styles - matching updates tab
  searchContainer: {
    flex: 1,
    backgroundColor: '#1F2937', // Gray-black background
  },
  searchHeader: {
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 16,
    height: 90,
    backgroundColor: '#374151', // Dark gray header
    borderBottomWidth: 1,
    borderBottomColor: '#4B5563',
    justifyContent: 'flex-end', // Push content to bottom
  },
  searchHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    height: 40, // Fixed height for proper centering
  },
  connectionIndicator: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 16,
    backgroundColor: 'rgba(255,255,255,0.1)',
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4B5563',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 8,
    borderWidth: 1,
    borderColor: '#6B7280',
    height: 40,
  },
  searchIcon: {
    marginRight: 4,
  },
  clearButton: {
    padding: 8,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 32,
    minHeight: 32,
    marginLeft: 4,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#F9FAFB',
    paddingVertical: 0,
    paddingHorizontal: 0,
    textAlignVertical: 'center',
    includeFontPadding: false,
    height: 24,
  },

  // Filters
  filtersContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#374151',
    borderBottomWidth: 1,
    borderBottomColor: '#4B5563',
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 12,
    gap: 8,
  },
  activeFilterButton: {
    backgroundColor: '#667eea',
  },
  inactiveFilterButton: {
    backgroundColor: '#4B5563',
  },
  filterText: {
    fontSize: 14,
    fontWeight: '600',
  },
  activeFilterText: {
    color: '#FFFFFF',
  },
  inactiveFilterText: {
    color: '#9CA3AF',
  },

  // Results
  resultsContainer: {
    flex: 1,
  },
  searchLoadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
    backgroundColor: '#1F2937',
  },
  searchLoadingText: {
    fontSize: 16,
    color: '#9CA3AF',
    fontWeight: '500',
  },
  searchResultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#374151',
    marginHorizontal: 12,
    marginVertical: 4,
    borderRadius: 12,
    gap: 12,
    borderWidth: 1,
    borderColor: '#4B5563',
  },
  resultIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  resultAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  searchResultContent: {
    flex: 1,
    gap: 6,
  },
  searchResultUsername: {
    fontSize: 16,
    fontWeight: '600',
    color: '#F9FAFB',
  },
  searchResultCaption: {
    fontSize: 14,
    color: '#D1D5DB',
    lineHeight: 20,
  },
  searchResultTime: {
    fontSize: 12,
    color: '#9CA3AF',
    fontWeight: '500',
  },
  resultTypeContainer: {
    alignItems: 'center',
  },
  resultTypeText: {
    fontSize: 10,
    color: '#9CA3AF',
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  searchEmptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
    paddingHorizontal: 32,
    backgroundColor: '#1F2937',
  },
  searchEmptyText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#F9FAFB',
    textAlign: 'center',
  },
  searchEmptySubtext: {
    fontSize: 16,
    color: '#9CA3AF',
    textAlign: 'center',
    lineHeight: 24,
  },
  searchInitialContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
    paddingHorizontal: 32,
    backgroundColor: '#1F2937',
  },
  searchInitialText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#F9FAFB',
    textAlign: 'center',
  },
  searchInitialSubtext: {
    fontSize: 16,
    color: '#9CA3AF',
    textAlign: 'center',
    lineHeight: 24,
  },
});
