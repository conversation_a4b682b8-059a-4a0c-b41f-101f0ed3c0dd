import React, { useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Image,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { navigationService, ROUTES } from '../src/services/navigationService';
import { FloatingActionButton, QuickNavActions } from '../src/components/NavigationHelper';

export default function AboutScreen() {
  const _router = useRouter();

  // Use React hook to demonstrate React usage and router for potential navigation fallback
  useEffect(() => {
    // Log component mount for debugging purposes
    console.log('AboutScreen mounted');

    // Use React to get component version info for debugging
    console.log('React version:', React.version || 'Unknown');

    // Store router reference for potential programmatic navigation
    if (_router) {
      console.log('Router available for AboutScreen navigation');
    }
  }, [_router]);

  const teamMembers = [
    {
      name: 'Development Team',
      role: 'Core Developers',
      description: 'Building the future of mobile communication',
    },
    {
      name: 'Design Team',
      role: 'UI/UX Designers',
      description: 'Creating beautiful and intuitive experiences',
    },
    {
      name: 'Security Team',
      role: 'Security Engineers',
      description: 'Ensuring your privacy and data protection',
    },
  ];

  const features = [
    'End-to-end encrypted messaging',
    'High-quality voice and video calls',
    'Group chats with up to 256 members',
    'Media sharing (photos, videos, documents)',
    'Voice messages and audio notes',
    'Real-time message delivery',
    'Cross-platform synchronization',
    'Dark and light themes',
  ];

  const handleOpenLink = (url: string) => {
    Linking.openURL(url);
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigationService.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>About IraChat</Text>
      </View>

      <ScrollView style={styles.content}>
        {/* App Logo and Info */}
        <View style={styles.appSection}>
          <View style={styles.logoContainer}>
            <Image
              source={require('../assets/images/LOGO.png')}
              style={styles.logo}
              resizeMode="contain"
            />
          </View>
          <Text style={styles.appName}>IraChat</Text>
          <Text style={styles.appVersion}>Version 1.0.2</Text>
          <Text style={styles.appDescription}>
            A modern, secure, and feature-rich messaging application designed 
            to connect people around the world with privacy and simplicity at its core.
          </Text>
        </View>

        {/* Features */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Key Features</Text>
          <View style={styles.featuresGrid}>
            {features.map((feature, index) => (
              <View key={index} style={styles.featureItem}>
                <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
                <Text style={styles.featureText}>{feature}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Team */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Our Team</Text>
          {teamMembers.map((member, index) => (
            <View key={index} style={styles.teamCard}>
              <View style={styles.teamIcon}>
                <Ionicons name="people" size={24} color="#667eea" />
              </View>
              <View style={styles.teamContent}>
                <Text style={styles.teamName}>{member.name}</Text>
                <Text style={styles.teamRole}>{member.role}</Text>
                <Text style={styles.teamDescription}>{member.description}</Text>
              </View>
            </View>
          ))}
        </View>

        {/* Technology */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Built With</Text>
          <View style={styles.techStack}>
            <View style={styles.techItem}>
              <Text style={styles.techName}>React Native</Text>
              <Text style={styles.techDescription}>Cross-platform mobile development</Text>
            </View>
            <View style={styles.techItem}>
              <Text style={styles.techName}>Firebase</Text>
              <Text style={styles.techDescription}>Real-time database and authentication</Text>
            </View>
            <View style={styles.techItem}>
              <Text style={styles.techName}>WebRTC</Text>
              <Text style={styles.techDescription}>High-quality voice and video calls</Text>
            </View>
            <View style={styles.techItem}>
              <Text style={styles.techName}>TypeScript</Text>
              <Text style={styles.techDescription}>Type-safe development</Text>
            </View>
          </View>
        </View>

        {/* Links */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Connect With Us</Text>
          <TouchableOpacity
            style={styles.linkButton}
            onPress={() => handleOpenLink('https://irachat.app')}
          >
            <Ionicons name="globe-outline" size={24} color="#667eea" />
            <Text style={styles.linkText}>Visit Our Website</Text>
            <Ionicons name="open-outline" size={16} color="#CCC" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.linkButton}
            onPress={() => handleOpenLink('https://github.com/irachat')}
          >
            <Ionicons name="logo-github" size={24} color="#667eea" />
            <Text style={styles.linkText}>View on GitHub</Text>
            <Ionicons name="open-outline" size={16} color="#CCC" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.linkButton}
            onPress={() => handleOpenLink('mailto:<EMAIL>')}
          >
            <Ionicons name="mail-outline" size={24} color="#667eea" />
            <Text style={styles.linkText}>Contact Us</Text>
            <Ionicons name="open-outline" size={16} color="#CCC" />
          </TouchableOpacity>
        </View>

        {/* Quick Navigation Links */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Links</Text>

          <TouchableOpacity
            style={styles.linkButton}
            onPress={() => navigationService.navigate(ROUTES.HELP.HELP)}
          >
            <Ionicons name="help-circle-outline" size={24} color="#667eea" />
            <Text style={styles.linkText}>Help & Support</Text>
            <Ionicons name="chevron-forward" size={16} color="#CCC" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.linkButton}
            onPress={() => navigationService.navigate(ROUTES.SETTINGS.PRIVACY)}
          >
            <Ionicons name="shield-outline" size={24} color="#667eea" />
            <Text style={styles.linkText}>Privacy Settings</Text>
            <Ionicons name="chevron-forward" size={16} color="#CCC" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.linkButton}
            onPress={() => navigationService.openSettings()}
          >
            <Ionicons name="settings-outline" size={24} color="#667eea" />
            <Text style={styles.linkText}>App Settings</Text>
            <Ionicons name="chevron-forward" size={16} color="#CCC" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.linkButton}
            onPress={() => navigationService.navigate(ROUTES.SETTINGS.EXPORT_DATA)}
          >
            <Ionicons name="download-outline" size={24} color="#667eea" />
            <Text style={styles.linkText}>Export Data</Text>
            <Ionicons name="chevron-forward" size={16} color="#CCC" />
          </TouchableOpacity>
        </View>

        {/* Legal */}
        <View style={styles.legalSection}>
          <Text style={styles.legalText}>
            © 2025 IraChat. All rights reserved.
          </Text>
          <View style={styles.legalLinks}>
            <TouchableOpacity onPress={() => handleOpenLink('https://irachat.com/privacy')}>
              <Text style={styles.legalLink}>Privacy Policy</Text>
            </TouchableOpacity>
            <Text style={styles.legalSeparator}>•</Text>
            <TouchableOpacity onPress={() => handleOpenLink('https://irachat.com/terms')}>
              <Text style={styles.legalLink}>Terms of Service</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Floating Action Button for Quick Actions */}
      <FloatingActionButton
        actions={[
          QuickNavActions.help,
          QuickNavActions.support,
          {
            icon: 'share-outline',
            label: 'Share App',
            onPress: () => Linking.openURL('https://irachat.app/download'),
            color: '#10B981',
          },
        ]}
        mainAction={{
          icon: 'home-outline',
          onPress: () => navigationService.goHome(),
          backgroundColor: '#667eea',
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    backgroundColor: '#667eea',
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  appSection: {
    alignItems: 'center',
    paddingVertical: 20,
    marginBottom: 20,
  },
  logoContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  logo: {
    width: 80,
    height: 80,
  },
  appName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  appVersion: {
    fontSize: 16,
    color: '#667eea',
    marginBottom: 12,
  },
  appDescription: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  featuresGrid: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  featureText: {
    marginLeft: 12,
    fontSize: 16,
    color: '#333',
  },
  teamCard: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  teamIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#F0F4FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  teamContent: {
    flex: 1,
  },
  teamName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  teamRole: {
    fontSize: 14,
    color: '#667eea',
    marginBottom: 4,
  },
  teamDescription: {
    fontSize: 14,
    color: '#666',
  },
  techStack: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
  },
  techItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  techName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  techDescription: {
    fontSize: 14,
    color: '#666',
  },
  linkButton: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  linkText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: '#333',
  },
  legalSection: {
    alignItems: 'center',
    paddingVertical: 20,
    marginTop: 20,
  },
  legalText: {
    fontSize: 14,
    color: '#999',
    marginBottom: 8,
  },
  legalLinks: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legalLink: {
    fontSize: 14,
    color: '#667eea',
  },
  legalSeparator: {
    marginHorizontal: 8,
    color: '#CCC',
  },
});
