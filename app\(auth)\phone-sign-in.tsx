// Phone Sign In Screen for IraChat
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useState, useEffect } from 'react';
import {
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { useDispatch } from 'react-redux';
import { signInUser } from '../../src/services/authService';
import { setUser } from '../../src/redux/userSlice';
import { AuthCredentials } from '../../src/types';
import { AnimatedButton } from '../../src/components/ui/AnimatedButton';
import { IraChatWallpaper } from '../../src/components/ui/IraChatWallpaper';
import PhoneNumberInput from '../../src/components/ui/PhoneNumberInput';
import { IRACHAT_COLORS, SPACING, BORDER_RADIUS } from '../../src/styles/iraChatDesignSystem';
import { textStyles } from '../../src/styles/designSystem';

const PhoneSignInScreen: React.FC = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState<'phone' | 'verification'>('phone');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [countryCode, setCountryCode] = useState('+256');
  const [verificationCode, setVerificationCode] = useState('');
  const [countdown, setCountdown] = useState(0);

  // Countdown timer for resend
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  const validatePhoneNumber = () => {
    if (!phoneNumber.trim()) {
      Alert.alert('Validation Error', 'Please enter your phone number');
      return false;
    }

    if (phoneNumber.length < 9) {
      Alert.alert('Validation Error', 'Please enter a valid phone number');
      return false;
    }

    return true;
  };

  const handleSendCode = async () => {
    if (!validatePhoneNumber()) return;

    setIsLoading(true);
    try {
      const fullPhoneNumber = `${countryCode}${phoneNumber}`;
      
      const credentials: AuthCredentials = {
        phoneNumber: fullPhoneNumber,
        method: 'phone',
      };

      const result = await signInUser(credentials);

      if (result.success) {
        if (result.requiresVerification) {
          setStep('verification');
          setCountdown(60);
          Alert.alert(
            'Verification Code Sent',
            `We've sent a verification code to ${fullPhoneNumber}`
          );
        } else if (result.user) {
          dispatch(setUser(result.user));
          router.replace('/(tabs)');
        }
      } else {
        Alert.alert('Sign In Failed', result.message || 'Failed to send verification code');
      }
    } catch (error: any) {
      console.error('❌ Phone sign in error:', error);
      Alert.alert('Error', 'Failed to send verification code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyCode = async () => {
    if (!verificationCode.trim()) {
      Alert.alert('Validation Error', 'Please enter the verification code');
      return;
    }

    if (verificationCode.length !== 6) {
      Alert.alert('Validation Error', 'Please enter a valid 6-digit code');
      return;
    }

    setIsLoading(true);
    try {
      const credentials: AuthCredentials = {
        phoneNumber: `${countryCode}${phoneNumber}`,
        verificationCode: verificationCode,
        method: 'phone',
      };

      const result = await signInUser(credentials);

      if (result.success && result.user) {
        dispatch(setUser(result.user));
        Alert.alert(
          'Welcome Back!',
          'You have successfully signed in to IraChat.',
          [
            {
              text: 'Continue',
              onPress: () => router.replace('/(tabs)'),
            },
          ]
        );
      } else {
        Alert.alert('Verification Failed', result.message || 'Invalid verification code');
      }
    } catch (error: any) {
      console.error('❌ Phone verification error:', error);
      Alert.alert('Error', 'Failed to verify code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = () => {
    if (countdown > 0) return;
    handleSendCode();
  };

  const renderPhoneStep = () => (
    <>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
          disabled={isLoading}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Sign In with Phone</Text>
          <Text style={styles.headerSubtitle}>Enter your phone number</Text>
        </View>
      </View>

      {/* Form */}
      <View style={styles.formContainer}>
        <PhoneNumberInput
          value={phoneNumber}
          onChangeText={setPhoneNumber}
          onCountryChange={setCountryCode}
          initialCountryCode={countryCode}
          editable={!isLoading}
        />

        <AnimatedButton
          title={isLoading ? "Sending Code..." : "Send Verification Code"}
          onPress={handleSendCode}
          variant="gradient"
          size="large"
          fullWidth={true}
          style={styles.sendButton}
          disabled={isLoading}
          icon={isLoading ? undefined : "send"}
          iconPosition="left"
        />

        {isLoading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color="#FFFFFF" />
            <Text style={styles.loadingText}>Sending verification code...</Text>
          </View>
        )}
      </View>
    </>
  );

  const renderVerificationStep = () => (
    <>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => setStep('phone')}
          disabled={isLoading}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Enter Verification Code</Text>
          <Text style={styles.headerSubtitle}>
            Code sent to {countryCode}{phoneNumber}
          </Text>
        </View>
      </View>

      {/* Form */}
      <View style={styles.formContainer}>
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Verification Code</Text>
          <View style={styles.inputWrapper}>
            <Ionicons name="shield-checkmark" size={20} color="rgba(255, 255, 255, 0.7)" style={styles.inputIcon} />
            <TextInput
              style={styles.textInput}
              placeholder="Enter 6-digit code"
              placeholderTextColor="rgba(255, 255, 255, 0.5)"
              value={verificationCode}
              onChangeText={setVerificationCode}
              keyboardType="number-pad"
              maxLength={6}
              autoFocus={true}
              editable={!isLoading}
            />
          </View>
        </View>

        <AnimatedButton
          title={isLoading ? "Verifying..." : "Verify & Sign In"}
          onPress={handleVerifyCode}
          variant="gradient"
          size="large"
          fullWidth={true}
          style={styles.verifyButton}
          disabled={isLoading}
          icon={isLoading ? undefined : "checkmark-circle"}
          iconPosition="left"
        />

        {/* Resend Code */}
        <View style={styles.resendContainer}>
          <Text style={styles.resendText}>Didn't receive the code? </Text>
          <TouchableOpacity 
            onPress={handleResendCode}
            disabled={countdown > 0 || isLoading}
          >
            <Text style={[
              styles.resendLink,
              (countdown > 0 || isLoading) && styles.resendLinkDisabled
            ]}>
              {countdown > 0 ? `Resend in ${countdown}s` : 'Resend Code'}
            </Text>
          </TouchableOpacity>
        </View>

        {isLoading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color="#FFFFFF" />
            <Text style={styles.loadingText}>Verifying code...</Text>
          </View>
        )}
      </View>
    </>
  );

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor={IRACHAT_COLORS.primary} />
      <IraChatWallpaper />
      
      <SafeAreaView style={styles.safeArea}>
        <KeyboardAvoidingView 
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardView}
        >
          <ScrollView 
            contentContainerStyle={styles.scrollContent}
            keyboardShouldPersistTaps="handled"
          >
            {step === 'phone' ? renderPhoneStep() : renderVerificationStep()}

            {/* Footer Links */}
            <View style={styles.footerContainer}>
              {/* Alternative Sign In */}
              <View style={styles.alternativeContainer}>
                <Text style={styles.alternativeText}>Or sign in with</Text>
                <TouchableOpacity
                  style={styles.emailButton}
                  onPress={() => router.push('/(auth)/email-sign-in')}
                  disabled={isLoading}
                >
                  <Ionicons name="mail" size={20} color="#007AFF" />
                  <Text style={styles.emailButtonText}>Email Address</Text>
                </TouchableOpacity>
              </View>

              {/* Create Account Link */}
              <View style={styles.createAccountContainer}>
                <Text style={styles.createAccountText}>Don't have an account? </Text>
                <TouchableOpacity 
                  onPress={() => router.push('/(auth)/phone-register')}
                  disabled={isLoading}
                >
                  <Text style={styles.createAccountLink}>Create Account</Text>
                </TouchableOpacity>
              </View>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: IRACHAT_COLORS.primary,
  },
  safeArea: {
    flex: 1,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: SPACING.lg,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: SPACING.md,
    marginBottom: SPACING.xl,
  },
  backButton: {
    padding: SPACING.sm,
    marginRight: SPACING.md,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    ...textStyles.h2,
    color: '#FFFFFF',
    fontWeight: '700' as const,
  },
  headerSubtitle: {
    ...textStyles.body,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
  },
  formContainer: {
    flex: 1,
    gap: SPACING.lg,
  },
  inputContainer: {
    marginBottom: SPACING.md,
  },
  inputLabel: {
    ...textStyles.caption,
    color: '#FFFFFF',
    fontWeight: '600' as const,
    marginBottom: SPACING.xs,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: SPACING.md,
    height: 56,
  },
  inputIcon: {
    marginRight: SPACING.sm,
  },
  textInput: {
    flex: 1,
    ...textStyles.body,
    color: '#FFFFFF',
    height: '100%',
    textAlign: 'center',
    fontSize: 18,
    letterSpacing: 2,
  },
  sendButton: {
    marginTop: SPACING.md,
  },
  verifyButton: {
    marginTop: SPACING.md,
  },
  resendContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: SPACING.sm,
  },
  resendText: {
    ...textStyles.caption,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  resendLink: {
    ...textStyles.caption,
    color: '#FFFFFF',
    fontWeight: '700' as const,
    textDecorationLine: 'underline',
  },
  resendLinkDisabled: {
    color: 'rgba(255, 255, 255, 0.5)',
    textDecorationLine: 'none',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: SPACING.sm,
  },
  loadingText: {
    ...textStyles.caption,
    color: 'rgba(255, 255, 255, 0.8)',
    marginLeft: SPACING.sm,
  },
  footerContainer: {
    paddingTop: SPACING.xl,
    paddingBottom: SPACING.lg,
  },
  alternativeContainer: {
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  alternativeText: {
    ...textStyles.caption,
    color: 'rgba(255, 255, 255, 0.6)',
    marginBottom: SPACING.sm,
  },
  emailButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.sm,
    gap: SPACING.xs,
  },
  emailButtonText: {
    ...textStyles.caption,
    color: '#007AFF',
    fontWeight: '600' as const,
  },
  createAccountContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  createAccountText: {
    ...textStyles.body,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  createAccountLink: {
    ...textStyles.body,
    color: '#FFFFFF',
    fontWeight: '700' as const,
    textDecorationLine: 'underline',
  },
});

export default PhoneSignInScreen;
