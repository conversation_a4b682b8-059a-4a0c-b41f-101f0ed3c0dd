import { Ionicons } from "@expo/vector-icons";
import { Tabs, usePathname, useSegments } from "expo-router";
import React from "react";
import { Keyboard, Platform, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import ErrorBoundary from "../../src/components/ErrorBoundary";
import { SwipeTabNavigator } from "../../src/components/SwipeTabNavigator";
import TabBarBackground from "../../src/components/ui/TabBarBackground";
import {
  headerSizes,
  isVerySmallDevice,
  tabBarSizes,
} from "../../src/utils/responsive";

export default function TabLayout() {
  // Get safe area insets
  const insets = useSafeAreaInsets();

  // Get current pathname and segments to detect profile page
  const pathname = usePathname();
  const segments = useSegments();
  const isProfilePage = pathname.includes('profile') || segments.includes('profile');

  // Debug log to see the actual pathname and segments
  React.useEffect(() => {
    console.log('🔍 Current pathname:', pathname);
    console.log('🔍 Current segments:', segments);
    console.log('🔍 Is profile page:', isProfilePage);
  }, [pathname, segments, isProfilePage]);

  // Get keyboard visibility for tab bar
  const [shouldHideTabBar, setShouldHideTabBar] = React.useState(false);

  // Listen to keyboard events
  React.useEffect(() => {
    const keyboardWillShow = () => setShouldHideTabBar(true);
    const keyboardWillHide = () => setShouldHideTabBar(false);
    const keyboardDidShow = () => setShouldHideTabBar(true);
    const keyboardDidHide = () => setShouldHideTabBar(false);

    const showEvent = Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow';
    const hideEvent = Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide';

    const showListener = Keyboard.addListener(showEvent, keyboardWillShow);
    const hideListener = Keyboard.addListener(hideEvent, keyboardWillHide);
    const didShowListener = Keyboard.addListener('keyboardDidShow', keyboardDidShow);
    const didHideListener = Keyboard.addListener('keyboardDidHide', keyboardDidHide);

    return () => {
      showListener?.remove();
      hideListener?.remove();
      didShowListener?.remove();
      didHideListener?.remove();
    };
  }, []);

  // Get responsive sizes
  const tabHeight = tabBarSizes.height;
  const tabFontSize = tabBarSizes.fontSize;
  const tabIconSize = tabBarSizes.iconSize;
  const headerHeight = headerSizes.height;
  const headerFontSize = headerSizes.fontSize;
  const headerIconSize = headerSizes.iconSize;

  return (
    <ErrorBoundary>
      <SwipeTabNavigator>
        <View
          style={{ flex: 1 }}
          accessible={true}
          accessibilityRole="tablist"
          accessibilityLabel="Main navigation"
        >
        <Tabs
        screenOptions={{
          // OPTIMIZED FOR PERFORMANCE - Reduced animations and faster loading
          tabBarActiveTintColor: "#667eea", // IraChat primary color - improved contrast
          tabBarInactiveTintColor: "#9CA3AF", // Better gray for inactive tabs
          lazy: false, // Disable lazy loading for faster tab switching
          // animationEnabled is not a valid option for bottom tabs
          tabBarStyle: Platform.select({
            ios: {
              backgroundColor: "transparent",
              borderTopWidth: 0,
              paddingBottom: Math.max(
                insets.bottom + 8,
                tabBarSizes.paddingBottom + 8,
              ), // Increased padding
              height:
                tabHeight +
                Math.max(insets.bottom + 8, tabBarSizes.paddingBottom + 8),
              position: "absolute",
              // Hide tab bar when keyboard is visible or on profile page
              display: shouldHideTabBar || isProfilePage ? 'none' : 'flex',
            },
            default: {
              backgroundColor: "#FFFFFF",
              borderTopWidth: 1,
              borderTopColor: "#E5E7EB",
              paddingBottom: Math.max(
                insets.bottom + 8,
                tabBarSizes.paddingBottom + 8,
              ), // Increased padding
              height: tabHeight + Math.max(insets.bottom + 8, 16), // Increased height
              // Hide tab bar when keyboard is visible or on profile page
              display: shouldHideTabBar || isProfilePage ? 'none' : 'flex',
            },
          }),
          tabBarBackground: () => <TabBarBackground />,
          tabBarLabelStyle: {
            fontSize: tabFontSize,
            fontWeight: "600",
            marginBottom: isVerySmallDevice() ? 2 : 4,
          },
          tabBarIconStyle: {
            marginTop: isVerySmallDevice() ? 2 : 4,
          },
          tabBarItemStyle: {
            borderRadius: 12,
            marginHorizontal: 4,
            paddingVertical: 4,
          },
          // Enhanced animations handled by SwipeableTabWrapper
          headerStyle: {
            backgroundColor: "#667eea", // Sky blue from welcome page
            elevation: 4,
            shadowOpacity: 0.3,
            height: headerHeight + insets.top,
            borderBottomWidth: 1,
            borderBottomColor: "rgba(255, 255, 255, 0.1)",
          },
          headerTitleContainerStyle: {
            paddingTop: insets.top, // Move paddingTop here to avoid warning
            justifyContent: "center", // Vertically center the title
            alignItems: "center", // Horizontally center the title
          },
          headerTintColor: "#FFFFFF",
          headerTitleStyle: {
            fontWeight: "bold",
            fontSize: headerFontSize,
            textAlign: "center", // Center text alignment
          },
          headerBackTitleStyle: {
            fontSize: headerIconSize, // Use headerIconSize for back button text
          },
          tabBarAccessibilityLabel: "Main navigation tabs",
          // tabBarAccessibilityRole: 'tablist', // Remove unsupported property
        }}
      >
        {/* Chats Tab */}
        <Tabs.Screen
          name="index"
          options={{
            title: "Chats",
            tabBarIcon: ({ color }) => (
              <Ionicons
                name="chatbubbles"
                size={tabIconSize}
                color={color}
                accessibilityLabel="Chats tab icon"
              />
            ),
            tabBarAccessibilityLabel: "Chats tab",
            headerShown: false, // Hide default header since we use custom MainHeader
          }}
        />

        {/* Groups Tab */}
        <Tabs.Screen
          name="groups"
          options={{
            title: "Groups",
            tabBarIcon: ({ color }) => (
              <Ionicons
                name="people"
                size={tabIconSize}
                color={color}
                accessibilityLabel="Groups tab icon"
              />
            ),
            tabBarAccessibilityLabel: "Groups tab",
            headerShown: false, // Hide default header since we use custom GroupsHeader
          }}
        />

        {/* Stories Tab (renamed from comprehensive-updates) */}
        <Tabs.Screen
          name="comprehensive-updates"
          options={{
            title: "Stories",
            tabBarIcon: ({ color }) => (
              <Ionicons
                name="radio-button-on"
                size={tabIconSize}
                color={color}
                accessibilityLabel="Stories tab icon"
              />
            ),
            tabBarAccessibilityLabel: "Stories tab",
            headerShown: false, // Hide default header since we use custom header
          }}
        />

        {/* Updates Tab */}
        <Tabs.Screen
          name="updates"
          options={{
            title: "Updates",
            tabBarIcon: ({ color }) => (
              <Ionicons
                name="newspaper"
                size={tabIconSize}
                color={color}
                accessibilityLabel="Updates tab icon"
              />
            ),
            tabBarAccessibilityLabel: "Updates tab",
            headerShown: false, // Hide default header since we use custom MainHeader
          }}
        />

        {/* Calls Tab */}
        <Tabs.Screen
          name="calls"
          options={{
            title: "Calls",
            tabBarIcon: ({ color }) => (
              <Ionicons
                name="call"
                size={tabIconSize}
                color={color}
                accessibilityLabel="Calls tab icon"
              />
            ),
            tabBarAccessibilityLabel: "Calls tab",
            headerShown: false, // Hide default header for consistency
          }}
        />

        {/* Profile and Settings are handled in overlay menu - no tabs needed */}
        <Tabs.Screen
          name="profile"
          options={{
            href: null, // Hide from tab bar - accessible via overlay menu
            headerShown: false,
            tabBarStyle: { display: 'none' }, // Hide tab bar on profile page
          }}
        />

        <Tabs.Screen
          name="settings"
          options={{
            href: null, // Hide from tab bar - accessible via overlay menu
            headerShown: false,
            tabBarStyle: { display: 'none' }, // Completely hide tab bar on settings page
          }}
        />
        </Tabs>
        </View>
      </SwipeTabNavigator>
    </ErrorBoundary>
  );
}
