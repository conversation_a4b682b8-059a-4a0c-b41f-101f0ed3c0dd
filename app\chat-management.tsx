import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { useState, useEffect, useCallback } from "react";
import {
    Alert,
    Text,
    TouchableOpacity,
    View,
    ActivityIndicator,
    Image,
    FlatList,
    Modal,
    TextInput,
    StyleSheet,
    Animated,
} from "react-native";
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { useSelector } from "react-redux";
import { RootState } from "../src/redux/store";
import { realChatService } from "../src/services/realChatService";
import { navigationService } from "../src/services/navigationService";
import { networkStateManager } from "../src/services/networkStateManager";
import { FloatingActionButton, QuickNavActions } from "../src/components/NavigationHelper";
import { ResponsiveContainer } from "../src/components/ui/ResponsiveContainer";
import { ResponsiveCard, ResponsiveListCard } from "../src/components/ui/ResponsiveCard";
import { AppHeader } from "../src/components/AppHeader";

import { IRACHAT_COLORS, BORDER_RADIUS, SHADOWS } from "../src/styles/iraChatDesignSystem";
import { ResponsiveScale, ResponsiveTypography, ResponsiveSpacing, DeviceInfo } from "../src/utils/responsiveUtils";

interface ChatItem {
  id: string;
  name: string;
  avatar?: string;
  lastMessage: string;
  timestamp: Date;
  messageCount: number;
  mediaCount: number;
  isGroup: boolean;
  isSelected: boolean;
}

export default function ChatManagementScreen() {
  const router = useRouter();
  const currentUser = useSelector((state: RootState) => state.user?.currentUser);

  const [chats, setChats] = useState<ChatItem[]>([]);
  const [filteredChats, setFilteredChats] = useState<ChatItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedChats, setSelectedChats] = useState<Set<string>>(new Set());
  const [selectAll, setSelectAll] = useState(false);
  const [showClearOptions, setShowClearOptions] = useState(false);
  const [clearingInProgress, setClearingInProgress] = useState(false);
  const [operationProgress, setOperationProgress] = useState({ current: 0, total: 0, currentChat: '' });
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  const [filterType, setFilterType] = useState<'all' | 'individual' | 'groups'>('all');
  const [sortBy, setSortBy] = useState<'name' | 'date' | 'size'>('date');
  const [lastOperation, setLastOperation] = useState<{
    type: 'clear';
    operation: 'messages' | 'media' | 'all';
    chatIds: string[];
    timestamp: Date;
  } | null>(null);
  const [showUndoOption, setShowUndoOption] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [isLoadingChats, setIsLoadingChats] = useState(false);
  const [contentOpacity] = useState(new Animated.Value(0));
  const [isOnline, setIsOnline] = useState(networkStateManager.isOnline());
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'error'>('idle');

  // Smart filtering and sorting logic
  const applyFiltersAndSort = useCallback((chatList: ChatItem[]) => {
    let filtered = [...chatList];

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(chat =>
        chat.name.toLowerCase().includes(query) ||
        chat.lastMessage.toLowerCase().includes(query)
      );
    }

    // Apply type filter
    if (filterType !== 'all') {
      filtered = filtered.filter(chat =>
        filterType === 'groups' ? chat.isGroup : !chat.isGroup
      );
    }

    // Apply smart sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'date':
          return b.timestamp.getTime() - a.timestamp.getTime();
        case 'size':
          return (b.messageCount + b.mediaCount) - (a.messageCount + a.mediaCount);
        default:
          return 0;
      }
    });

    setFilteredChats(filtered);

    // Smart selection management - remove selections for filtered out chats
    const filteredIds = new Set(filtered.map(chat => chat.id));
    const newSelected = new Set([...selectedChats].filter(id => filteredIds.has(id)));
    if (newSelected.size !== selectedChats.size) {
      setSelectedChats(newSelected);
      setSelectAll(newSelected.size === filtered.length && filtered.length > 0);
    }
  }, [searchQuery, filterType, sortBy, selectedChats]);

  const loadChats = useCallback(async (isRefresh = false) => {
    // Safety check to prevent infinite loops
    if (!currentUser?.id) {
      console.log('⚠️ No user ID available, skipping chat load');
      setChats([]);
      setLoading(false);
      setRefreshing(false);
      return;
    }

    // Prevent multiple simultaneous calls
    if (isLoadingChats) {
      console.log('⚠️ Already loading chats, skipping duplicate call');
      return;
    }

    setIsLoadingChats(true);

    if (isRefresh) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }

    // Instant loading - no delays

    try {
      // Load all chats from the real chat service with retry logic
      // The service automatically handles online/offline scenarios
      const result = await realChatService.getUserChats(currentUser.id);
      if (result.success && result.chats) {
        const chatItems: ChatItem[] = result.chats.map(chat => ({
          id: chat.id,
          name: chat.isGroup ? chat.groupName || 'Group Chat' : chat.participantName || 'Unknown',
          avatar: chat.isGroup ? chat.groupAvatar : chat.participantAvatar,
          lastMessage: chat.lastMessage?.text || 'No messages',
          timestamp: chat.lastMessage?.timestamp?.toDate() || new Date(),
          messageCount: chat.messageCount || 0,
          mediaCount: chat.mediaCount || 0,
          isGroup: chat.isGroup || false,
          isSelected: false,
        }));
        setChats(chatItems);
      } else {
        // Handle empty result gracefully
        console.log('⚠️ No chats found or service returned error:', result.error);
        setChats([]);
      }
    } catch (error) {
      console.error('Error loading chats:', error);

      // Set empty chats and show a simple error message
      setChats([]);

      // Only show alert if not refreshing to avoid spam
      if (!isRefresh) {
        setTimeout(() => {
          Alert.alert(
            'Connection Error',
            'Unable to load chats at the moment. Please check your connection and try again.',
            [
              { text: 'OK', style: 'default' }
            ]
          );
        }, 100);
      }
    } finally {
      // Instant loading - no minimum time delay
      setLoading(false);
      setRefreshing(false);
      setIsLoadingChats(false);
      // Quick fade in content
      Animated.timing(contentOpacity, {
        toValue: 1,
        duration: 100, // Very fast fade-in
        useNativeDriver: true,
      }).start();
    }
  }, [currentUser?.id]); // Removed errorRetryCount dependency to prevent infinite loop

  useEffect(() => {
    loadChats();
  }, [loadChats]);

  // Initialize content opacity based on loading state
  useEffect(() => {
    if (!loading) {
      contentOpacity.setValue(1);
    } else {
      contentOpacity.setValue(0);
    }
  }, [loading, contentOpacity]);

  // Auto-expand search when there's a query
  useEffect(() => {
    if (searchQuery && !isSearchExpanded) {
      setIsSearchExpanded(true);
    }
  }, [searchQuery, isSearchExpanded]);

  // Apply filters whenever chats or filter criteria change
  useEffect(() => {
    applyFiltersAndSort(chats);
  }, [chats, applyFiltersAndSort]);

  // Network state monitoring for online/offline functionality
  useEffect(() => {
    const handleNetworkChange = (networkState: any) => {
      setIsOnline(networkState.isConnected);

      if (networkState.isConnected && !loading) {
        // When coming back online, sync any pending changes
        setSyncStatus('syncing');
        setTimeout(() => {
          loadChats(true); // Refresh data when back online
          setSyncStatus('idle');
        }, 1000);
      }
    };

    // Add network listener
    networkStateManager.addListener('chatManagement', handleNetworkChange, 1);

    return () => {
      networkStateManager.removeListener('chatManagement');
    };
  }, [loading, loadChats]);

  // Hide undo option after 10 seconds
  useEffect(() => {
    if (showUndoOption) {
      const timer = setTimeout(() => {
        setShowUndoOption(false);
        setLastOperation(null);
      }, 10000);
      return () => clearTimeout(timer);
    }
  }, [showUndoOption]);

  // Smart selection logic with better UX
  const toggleChatSelection = useCallback((chatId: string) => {
    const newSelected = new Set(selectedChats);
    if (newSelected.has(chatId)) {
      newSelected.delete(chatId);
    } else {
      newSelected.add(chatId);
    }
    setSelectedChats(newSelected);

    // Smart select all state management based on filtered chats
    const visibleChatIds = filteredChats.map(chat => chat.id);
    const selectedVisibleChats = visibleChatIds.filter(id => newSelected.has(id));
    setSelectAll(selectedVisibleChats.length === visibleChatIds.length && visibleChatIds.length > 0);
  }, [selectedChats, filteredChats]);

  const toggleSelectAll = useCallback(() => {
    const visibleChatIds = filteredChats.map(chat => chat.id);

    if (selectAll) {
      // Deselect all visible chats but keep selections from other filters
      const newSelected = new Set([...selectedChats].filter(id => !visibleChatIds.includes(id)));
      setSelectedChats(newSelected);
      setSelectAll(false);
    } else {
      // Select all visible chats
      const newSelected = new Set([...selectedChats, ...visibleChatIds]);
      setSelectedChats(newSelected);
      setSelectAll(true);
    }
  }, [selectAll, filteredChats, selectedChats]);

  // Smart bulk selection helpers
  const selectByType = useCallback((type: 'groups' | 'individual') => {
    const targetChats = filteredChats.filter(chat =>
      type === 'groups' ? chat.isGroup : !chat.isGroup
    );
    const newSelected = new Set([...selectedChats, ...targetChats.map(chat => chat.id)]);
    setSelectedChats(newSelected);
  }, [filteredChats, selectedChats]);

  const selectByActivity = useCallback((criteria: 'active' | 'inactive') => {
    const now = new Date();
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const targetChats = filteredChats.filter(chat =>
      criteria === 'active' ? chat.timestamp > weekAgo : chat.timestamp <= weekAgo
    );
    const newSelected = new Set([...selectedChats, ...targetChats.map(chat => chat.id)]);
    setSelectedChats(newSelected);
  }, [filteredChats, selectedChats]);

  const handleClearSelected = useCallback(() => {
    if (selectedChats.size === 0) {
      Alert.alert('No Selection', 'Please select chats to clear');
      return;
    }

    // Show smart confirmation with details
    const selectedChatNames = chats
      .filter(chat => selectedChats.has(chat.id))
      .map(chat => chat.name)
      .slice(0, 3);

    const displayNames = selectedChatNames.join(', ') +
      (selectedChats.size > 3 ? ` and ${selectedChats.size - 3} more` : '');

    Alert.alert(
      'Clear Chat Data',
      `You are about to clear data from:\n${displayNames}\n\nWhat would you like to clear?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Show Options', onPress: () => setShowClearOptions(true) }
      ]
    );
  }, [selectedChats, chats]);

  const executeClearOperation = async (operation: 'messages' | 'media' | 'all') => {
    setClearingInProgress(true);
    setShowClearOptions(false);

    try {
      const selectedChatIds = Array.from(selectedChats);
      let operationText = '';

      switch (operation) {
        case 'messages':
          operationText = 'messages only';
          break;
        case 'media':
          operationText = 'media files only';
          break;
        case 'all':
          operationText = 'all data';
          break;
      }

      // Show offline notice if not connected
      if (!isOnline) {
        Alert.alert(
          'Offline Mode',
          `You're currently offline. The operation will be performed locally and synced when you're back online.`,
          [
            { text: 'Cancel', style: 'cancel', onPress: () => setClearingInProgress(false) },
            { text: 'Continue', onPress: () => proceedWithClearOperation(selectedChatIds, operation, operationText) }
          ]
        );
        return;
      }

      await proceedWithClearOperation(selectedChatIds, operation, operationText);
    } catch (error) {
      console.error('Error in executeClearOperation:', error);
      setClearingInProgress(false);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    }
  };

  const proceedWithClearOperation = async (selectedChatIds: string[], operation: 'messages' | 'media' | 'all', operationText: string) => {
    try {

      // Initialize progress tracking
      setOperationProgress({ current: 0, total: selectedChatIds.length, currentChat: '' });

      // Store operation for potential undo
      setLastOperation({
        type: 'clear',
        operation,
        chatIds: selectedChatIds,
        timestamp: new Date()
      });

      // Use batch operation for better performance when possible
      if (selectedChatIds.length > 1) {
        try {
          const clearOptions = {
            clearMessages: operation === 'messages' || operation === 'all',
            clearMedia: operation === 'media' || operation === 'all',
            clearAll: operation === 'all'
          };

          const batchResult = await realChatService.clearMultipleChats(selectedChatIds, clearOptions);

          if (batchResult.success && batchResult.results) {
            const successCount = Object.values(batchResult.results).filter(success => success).length;
            const failureCount = selectedChatIds.length - successCount;

            // Optimistic UI update - remove cleared chats from UI immediately
            if (operation === 'all') {
              setChats(prev => prev.filter(chat => !selectedChatIds.includes(chat.id)));
            } else {
              // Update message/media counts optimistically
              setChats(prev => prev.map(chat => {
                if (selectedChatIds.includes(chat.id) && batchResult.results![chat.id]) {
                  return {
                    ...chat,
                    messageCount: operation === 'messages' ? 0 : chat.messageCount,
                    mediaCount: operation === 'media' ? 0 : chat.mediaCount,
                    lastMessage: operation === 'messages' ? 'No messages' : chat.lastMessage
                  };
                }
                return chat;
              }));
            }

            handleOperationComplete(successCount, failureCount, operationText, selectedChatIds);
            return;
          }
        } catch (batchError) {
          console.warn('Batch operation failed, falling back to individual operations:', batchError);
        }
      }

      // Fallback to individual operations with progress tracking
      const results: { [chatId: string]: boolean } = {};

      for (let i = 0; i < selectedChatIds.length; i++) {
        const chatId = selectedChatIds[i];
        const chatName = chats.find(c => c.id === chatId)?.name || 'Unknown';

        setOperationProgress({
          current: i + 1,
          total: selectedChatIds.length,
          currentChat: chatName
        });

        try {
          let result;
          switch (operation) {
            case 'messages':
              result = await realChatService.clearChatMessages(chatId);
              break;
            case 'media':
              result = await realChatService.clearChatMedia(chatId);
              break;
            case 'all':
              result = await realChatService.clearChatCompletely(chatId);
              break;
            default:
              result = { success: false, error: 'Invalid operation' };
          }
          results[chatId] = result.success;

          // Optimistic UI update for each successful operation
          if (result.success) {
            if (operation === 'all') {
              setChats(prev => prev.filter(chat => chat.id !== chatId));
            } else {
              setChats(prev => prev.map(chat => {
                if (chat.id === chatId) {
                  return {
                    ...chat,
                    messageCount: operation === 'messages' ? 0 : chat.messageCount,
                    mediaCount: operation === 'media' ? 0 : chat.mediaCount,
                    lastMessage: operation === 'messages' ? 'No messages' : chat.lastMessage
                  };
                }
                return chat;
              }));
            }
          }

          if (!result.success) {
            console.error(`Failed to clear ${operation} for chat ${chatId}:`, result.error);
          }
        } catch (error) {
          console.error(`Error clearing ${operation} for chat ${chatId}:`, error);
          results[chatId] = false;
        }
      }

      // Check if all operations were successful
      const successCount = Object.values(results).filter(success => success).length;
      const failureCount = selectedChatIds.length - successCount;

      handleOperationComplete(successCount, failureCount, operationText, selectedChatIds);
    } catch (error) {
      console.error('Error during clear operation:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setClearingInProgress(false);
      setOperationProgress({ current: 0, total: 0, currentChat: '' });
    }
  };

  // Handle operation completion with smart user feedback
  const handleOperationComplete = useCallback((
    successCount: number,
    failureCount: number,
    operationText: string,
    affectedChatIds: string[]
  ) => {
    // Show appropriate alert based on results
    if (failureCount === 0) {
      Alert.alert(
        'Success',
        `Successfully cleared ${operationText} from ${successCount} chat(s)`,
        [
          {
            text: 'OK',
            onPress: () => {
              setSelectedChats(new Set());
              setSelectAll(false);
              setShowUndoOption(true);
              // Refresh data to ensure consistency
              setTimeout(() => loadChats(true), 1000);
            }
          }
        ]
      );
    } else if (successCount === 0) {
      Alert.alert(
        'Error',
        `Failed to clear ${operationText} from all ${affectedChatIds.length} chat(s). Please try again.`,
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Retry', onPress: () => setShowClearOptions(true) }
        ]
      );
    } else {
      Alert.alert(
        'Partial Success',
        `Cleared ${operationText} from ${successCount} chat(s). ${failureCount} chat(s) failed to clear.`,
        [
          { text: 'OK', onPress: () => {
            setSelectedChats(new Set());
            setSelectAll(false);
            setShowUndoOption(true);
            setTimeout(() => loadChats(true), 1000);
          }},
          { text: 'Retry Failed', onPress: () => {
            // Select only the failed chats for retry
            const failedChats = affectedChatIds.filter((_, index) =>
              Object.values({})[index] === false // This would need the actual results
            );
            setSelectedChats(new Set(failedChats));
            setShowClearOptions(true);
          }}
        ]
      );
    }
  }, [loadChats]);



  // Undo functionality
  const handleUndo = useCallback(async () => {
    if (!lastOperation) return;

    Alert.alert(
      'Undo Operation',
      `This will attempt to restore the ${lastOperation.operation} data that was cleared. Note: This may not be fully reversible.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Undo',
          style: 'destructive',
          onPress: async () => {
            setShowUndoOption(false);
            setLastOperation(null);
            // In a real implementation, you might have backup data to restore
            Alert.alert('Info', 'Undo functionality would restore backed up data in a production app.');
            loadChats(true);
          }
        }
      ]
    );
  }, [lastOperation, loadChats]);

  // Smart refresh with pull-to-refresh
  const handleRefresh = useCallback(() => {
    loadChats(true);
  }, [loadChats]);

  const renderChatItem = ({ item, index }: { item: ChatItem; index: number }) => (
    <ResponsiveListCard
      onPress={() => {
        if (selectedChats.size > 0) {
          // If in selection mode, toggle selection
          toggleChatSelection(item.id);
        } else {
          // If not in selection mode, navigate to chat
          if (item.isGroup) {
            handleNavigation(`/group-chat?groupId=${item.id}`);
          } else {
            handleNavigation(`/chat/${item.id}`);
          }
        }
      }}
      index={index}
      animated={true}
      showDivider={false}
      style={{
        marginHorizontal: 0, // Removed horizontal margins
        marginVertical: ResponsiveSpacing.xs,
        borderRadius: 0, // Removed border radius for full width
        borderWidth: selectedChats.has(item.id) ? 2 : 0,
        borderColor: selectedChats.has(item.id) ? IRACHAT_COLORS.primary : 'transparent',
        ...SHADOWS.md,
      }}
    >
      {/* Selection Checkbox */}
      <View style={{
        width: 24,
        height: 24,
        borderRadius: 12,
        borderWidth: 2,
        borderColor: selectedChats.has(item.id) ? '#87CEEB' : '#ddd',
        backgroundColor: selectedChats.has(item.id) ? '#87CEEB' : 'transparent',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 12,
      }}>
        {selectedChats.has(item.id) && (
          <Ionicons name="checkmark" size={16} color="#FFFFFF" />
        )}
      </View>

      {/* Avatar */}
      <View style={{ marginRight: 12 }}>
        {item.avatar ? (
          <Image
            source={{ uri: item.avatar }}
            style={{ width: 48, height: 48, borderRadius: 24 }}
          />
        ) : (
          <View style={{
            width: 48,
            height: 48,
            borderRadius: 24,
            backgroundColor: '#87CEEB',
            justifyContent: 'center',
            alignItems: 'center',
          }}>
            <Ionicons 
              name={item.isGroup ? "people" : "person"} 
              size={24} 
              color="#FFFFFF" 
            />
          </View>
        )}
      </View>

      {/* Chat Info */}
      <View style={{ flex: 1 }}>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 4 }}>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#333',
            flex: 1,
          }}>
            {item.name}
          </Text>
          {item.isGroup && (
            <View style={{
              backgroundColor: '#87CEEB',
              paddingHorizontal: 8,
              paddingVertical: 2,
              borderRadius: 10,
              marginLeft: 8,
            }}>
              <Text style={{ color: 'white', fontSize: 10, fontWeight: '600' }}>
                GROUP
              </Text>
            </View>
          )}
        </View>
        
        <Text style={{
          fontSize: 14,
          color: '#666',
          marginBottom: 4,
        }} numberOfLines={1}>
          {item.lastMessage}
        </Text>
        
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
          <Text style={{ fontSize: 12, color: '#999' }}>
            {item.timestamp.toLocaleDateString()}
          </Text>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text style={{ fontSize: 12, color: '#87CEEB', marginRight: 8 }}>
              {item.messageCount} msgs
            </Text>
            <Text style={{ fontSize: 12, color: '#87CEEB' }}>
              {item.mediaCount} media
            </Text>
          </View>
        </View>
      </View>
    </ResponsiveListCard>
  );



  // Create responsive styles using StyleSheet - moved before loading check
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#000000', // Pure black background like other components
    },
    headerGradient: {
      paddingTop: DeviceInfo.statusBarHeight + ResponsiveSpacing.md, // Proper status bar respect + padding
      paddingBottom: 0, // Remove bottom padding to eliminate green gap
      paddingHorizontal: 0, // Removed left and right margins
    },
    headerRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: ResponsiveSpacing.md,
      minHeight: ResponsiveScale.spacing(44), // Ensure touch target
    },
    headerTitle: {
      fontSize: ResponsiveTypography.fontSize.xl,
      fontWeight: 'bold',
      color: '#FFFFFF',
      flex: 1,
      marginRight: ResponsiveSpacing.sm,
    },
    selectAllButton: {
      backgroundColor: 'rgba(255,255,255,0.15)',
      paddingHorizontal: ResponsiveSpacing.sm,
      paddingVertical: ResponsiveSpacing.xs,
      borderRadius: BORDER_RADIUS.md,
      borderWidth: 1,
      borderColor: 'rgba(255,255,255,0.2)',
      minHeight: ResponsiveScale.spacing(36),
      justifyContent: 'center',
      alignItems: 'center',
    },
    advancedOptionsButton: {
      backgroundColor: 'rgba(255,255,255,0.15)',
      paddingHorizontal: ResponsiveSpacing.sm,
      paddingVertical: ResponsiveSpacing.xs,
      borderRadius: BORDER_RADIUS.md,
      marginLeft: ResponsiveSpacing.xs,
      borderWidth: 1,
      borderColor: 'rgba(255,255,255,0.2)',
      minHeight: ResponsiveScale.spacing(36),
      justifyContent: 'center',
      alignItems: 'center',
    },

    tabsSection: {
      backgroundColor: '#000000', // Match content area background
      borderBottomWidth: 1,
      borderBottomColor: '#333333',
    },
    headerSearchContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: 'rgba(255, 255, 255, 0.15)',
      borderRadius: BORDER_RADIUS.lg,
      paddingHorizontal: ResponsiveSpacing.sm,
      paddingVertical: ResponsiveSpacing.xs,
      flex: 1,
      maxWidth: 350, // Increased width for better usability
    },
    headerSearchInput: {
      flex: 1,
      marginLeft: ResponsiveSpacing.xs,
      fontSize: ResponsiveTypography.fontSize.sm,
      color: '#FFFFFF',
      height: 28,
      textAlignVertical: 'center',
      includeFontPadding: false,
      paddingVertical: 0,
    },
    headerSearchClear: {
      padding: ResponsiveSpacing.xs,
      marginLeft: ResponsiveSpacing.xs,
    },
  });

  // Skeleton Loading Component with shimmer animation - moved after styles definition
  const SkeletonLoader = () => {
    const shimmerAnim = useState(new Animated.Value(0))[0];

    useEffect(() => {
      const shimmerAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(shimmerAnim, {
            toValue: 1,
            duration: 600, // Faster animation
            useNativeDriver: true,
          }),
          Animated.timing(shimmerAnim, {
            toValue: 0,
            duration: 600, // Faster animation
            useNativeDriver: true,
          }),
        ])
      );
      shimmerAnimation.start();
      return () => shimmerAnimation.stop();
    }, [shimmerAnim]);

    const shimmerOpacity = shimmerAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [0.5, 0.8], // More subtle opacity range
    });

    return (
    <ResponsiveContainer style={styles.container} paddingHorizontal={false}>
      <StatusBar style="light" backgroundColor="transparent" translucent={true} />

      {/* Real Header - Always visible during loading */}
      <LinearGradient
        colors={[IRACHAT_COLORS.primary, IRACHAT_COLORS.primaryDark]}
        style={styles.headerGradient}
      >
        <AppHeader
          title="Manage Your Chats"
          showBackButton={true}
          onBackPress={() => navigationService.goBack()}
          style={{ paddingTop: ResponsiveSpacing.md, paddingBottom: ResponsiveSpacing.md }}
          rightComponent={
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: ResponsiveSpacing.xs }}>
              <TouchableOpacity
                style={{
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  borderRadius: BORDER_RADIUS.md,
                  paddingHorizontal: ResponsiveSpacing.sm,
                  paddingVertical: ResponsiveSpacing.xs,
                }}
                disabled={true} // Disabled during loading
              >
                <Ionicons name="search-outline" size={20} color="rgba(255,255,255,0.7)" />
              </TouchableOpacity>
              <TouchableOpacity
                style={{
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  borderRadius: BORDER_RADIUS.md,
                  paddingHorizontal: ResponsiveSpacing.sm,
                  paddingVertical: ResponsiveSpacing.xs,
                  marginRight: ResponsiveSpacing.sm,
                }}
                disabled={true} // Disabled during loading
              >
                <Ionicons name="options-outline" size={20} color="rgba(255,255,255,0.7)" />
              </TouchableOpacity>
            </View>
          }
        />
      </LinearGradient>



      {/* Filter Tabs Skeleton - Subtle dark theme */}
      <View style={styles.tabsSection}>
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          paddingHorizontal: ResponsiveSpacing.md,
          paddingVertical: ResponsiveSpacing.sm,
        }}>
          <View style={{ flexDirection: 'row', gap: ResponsiveSpacing.xs }}>
            {[1, 2, 3].map((i) => (
              <View key={i} style={{
                width: 60,
                height: 28,
                backgroundColor: 'rgba(255,255,255,0.1)', // Subtle dark tabs
                borderRadius: BORDER_RADIUS.lg,
              }} />
            ))}
          </View>
          <View style={{
            width: 80,
            height: 28,
            backgroundColor: 'rgba(255,255,255,0.1)', // Subtle sort button
            borderRadius: BORDER_RADIUS.lg,
          }} />
        </View>
      </View>

      {/* Chat List Skeleton - Dark themed, subtle */}
      <View style={{ flex: 1, paddingHorizontal: ResponsiveSpacing.md }}>
        {[1, 2, 3, 4].map((i) => (
          <View key={i} style={{
            backgroundColor: 'rgba(255,255,255,0.05)', // Very subtle dark background
            borderRadius: BORDER_RADIUS.lg,
            padding: ResponsiveSpacing.md,
            marginBottom: ResponsiveSpacing.sm,
            flexDirection: 'row',
            alignItems: 'center',
            borderWidth: 1,
            borderColor: 'rgba(255,255,255,0.1)', // Subtle border
          }}>
            {/* Avatar Skeleton */}
            <Animated.View style={{
              width: 50,
              height: 50,
              backgroundColor: 'rgba(255,255,255,0.1)', // Dark subtle avatar
              borderRadius: 25,
              marginRight: ResponsiveSpacing.md,
              opacity: shimmerOpacity,
            }} />

            {/* Content Skeleton */}
            <View style={{ flex: 1 }}>
              <Animated.View style={{
                width: '70%',
                height: 16,
                backgroundColor: 'rgba(255,255,255,0.15)', // Subtle text line
                borderRadius: 8,
                marginBottom: 8,
                opacity: shimmerOpacity,
              }} />
              <Animated.View style={{
                width: '90%',
                height: 12,
                backgroundColor: 'rgba(255,255,255,0.08)', // Even more subtle
                borderRadius: 6,
                opacity: shimmerOpacity,
              }} />
            </View>

            {/* Right side skeleton */}
            <View style={{ alignItems: 'flex-end' }}>
              <Animated.View style={{
                width: 40,
                height: 12,
                backgroundColor: 'rgba(255,255,255,0.1)',
                borderRadius: 6,
                marginBottom: 8,
                opacity: shimmerOpacity,
              }} />
              <Animated.View style={{
                width: 20,
                height: 20,
                backgroundColor: 'rgba(255,255,255,0.08)',
                borderRadius: 10,
                opacity: shimmerOpacity,
              }} />
            </View>
          </View>
        ))}
      </View>

      {/* Minimal loading indicator */}
      <View style={{
        position: 'absolute',
        top: 120,
        right: 20,
        alignItems: 'center',
      }}>
        <View style={{
          backgroundColor: 'rgba(0,0,0,0.6)',
          paddingHorizontal: 12,
          paddingVertical: 8,
          borderRadius: 20,
          flexDirection: 'row',
          alignItems: 'center',
        }}>
          <ActivityIndicator size="small" color="#FFFFFF" />
          {!isOnline && (
            <Text style={{
              color: '#FFFFFF',
              marginLeft: 8,
              fontSize: 12,
            }}>
              Offline
            </Text>
          )}
        </View>
      </View>
    </ResponsiveContainer>
    );
  };

  // Check loading state and return skeleton if loading
  if (loading) {
    return <SkeletonLoader />;
  }

  // Navigation handler using router
  const handleNavigation = (route: string) => {
    router.push(route as any);
  };

  return (
    <Animated.View style={[{ flex: 1, opacity: contentOpacity }]}>
      <ResponsiveContainer style={styles.container} paddingHorizontal={false}>
        <StatusBar style="light" backgroundColor="transparent" translucent={true} />

      {/* Header with LinearGradient */}
      <LinearGradient
        colors={[IRACHAT_COLORS.primary, IRACHAT_COLORS.primaryDark]}
        style={styles.headerGradient}
      >
        <AppHeader
          title={isSearchExpanded ? "" : "Manage Your Chats"}
          showBackButton={true}
          onBackPress={() => navigationService.goBack()}
          style={{ paddingTop: ResponsiveSpacing.md, paddingBottom: ResponsiveSpacing.md }} // Add proper spacing
          rightComponent={
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: ResponsiveSpacing.xs }}>
              {/* Network Status Indicator */}
              <View style={{
                backgroundColor: isOnline ? 'rgba(16, 185, 129, 0.2)' : 'rgba(239, 68, 68, 0.2)',
                borderRadius: BORDER_RADIUS.sm,
                paddingHorizontal: ResponsiveSpacing.xs,
                paddingVertical: 2,
              }}>
                <Ionicons
                  name={isOnline ? "wifi" : "wifi-outline"}
                  size={14}
                  color={isOnline ? "#10B981" : "#EF4444"}
                />
              </View>

              {!isSearchExpanded && (
                <TouchableOpacity
                  onPress={() => setIsSearchExpanded(true)}
                  style={{
                    backgroundColor: 'rgba(255,255,255,0.2)',
                    borderRadius: BORDER_RADIUS.md,
                    paddingHorizontal: ResponsiveSpacing.sm,
                    paddingVertical: ResponsiveSpacing.xs,
                  }}
                >
                  <Ionicons name="search-outline" size={20} color="#FFFFFF" />
                </TouchableOpacity>
              )}
              <TouchableOpacity
                onPress={toggleSelectAll}
                style={styles.selectAllButton}
              >
                <Ionicons name={selectAll ? "checkbox" : "square-outline"} size={20} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
          }
          centerComponent={
            isSearchExpanded ? (
              <View style={styles.headerSearchContainer}>
                <Ionicons name="search" size={18} color="#FFFFFF" />
                <TextInput
                  placeholder="Search chats..."
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                  style={styles.headerSearchInput}
                  placeholderTextColor="rgba(255,255,255,0.7)"
                  autoFocus={true}
                />
                <TouchableOpacity
                  onPress={() => {
                    setSearchQuery('');
                    setIsSearchExpanded(false);
                  }}
                  style={styles.headerSearchClear}
                >
                  <Ionicons name="close" size={16} color="#FFFFFF" />
                </TouchableOpacity>
              </View>
            ) : null
          }
        />

      </LinearGradient>



      {/* Filter and Sort Options - Moved outside header */}
      <View style={styles.tabsSection}>
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: ResponsiveSpacing.xs,
          paddingHorizontal: ResponsiveSpacing.md,
          paddingVertical: ResponsiveSpacing.sm,
        }}>
          <View style={{
            flexDirection: 'row',
            flexWrap: 'wrap',
            gap: ResponsiveSpacing.xs,
          }}>
            {(['all', 'individual', 'groups'] as const).map((type) => (
              <TouchableOpacity
                key={type}
                onPress={() => setFilterType(type)}
                style={{
                  backgroundColor: filterType === type ? IRACHAT_COLORS.primary : 'rgba(255,255,255,0.1)',
                  paddingHorizontal: ResponsiveSpacing.sm,
                  paddingVertical: ResponsiveSpacing.xs,
                  borderRadius: BORDER_RADIUS.lg, // Added border radius for better appearance
                  borderWidth: 0, // Remove border
                  minHeight: ResponsiveScale.spacing(32),
                  justifyContent: 'center',
                  alignItems: 'center',
                  minWidth: ResponsiveScale.spacing(60),
                }}
              >
                <Text style={{
                  color: filterType === type ? '#FFFFFF' : '#CCCCCC',
                  fontSize: ResponsiveTypography.fontSize.sm,
                  fontWeight: '600',
                  textTransform: 'capitalize',
                }}>
                  {type}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          <TouchableOpacity
            onPress={() => {
              const sortOptions: typeof sortBy[] = ['date', 'name', 'size'];
              const currentIndex = sortOptions.indexOf(sortBy);
              const nextIndex = (currentIndex + 1) % sortOptions.length;
              setSortBy(sortOptions[nextIndex]);
            }}
            style={{
              backgroundColor: 'rgba(255,255,255,0.1)',
              paddingHorizontal: ResponsiveSpacing.sm,
              paddingVertical: ResponsiveSpacing.xs,
              borderRadius: BORDER_RADIUS.lg, // Added border radius for consistency
              flexDirection: 'row',
              alignItems: 'center',
              borderWidth: 0, // Remove border
              minHeight: ResponsiveScale.spacing(32),
              justifyContent: 'center',
            }}
          >
            <Ionicons name="swap-vertical" size={ResponsiveScale.iconSize(16)} color="#CCCCCC" />
            <Text style={{
              color: '#CCCCCC',
              fontSize: ResponsiveTypography.fontSize.sm,
              fontWeight: '600',
              marginLeft: ResponsiveSpacing.xs,
              textTransform: 'capitalize'
            }}>
              {sortBy}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Progress Indicator for Operations */}
      {clearingInProgress && (
        <View style={{
          backgroundColor: '#1A1A1A',
          padding: ResponsiveSpacing.md,
          marginHorizontal: ResponsiveSpacing.lg,
          marginTop: ResponsiveSpacing.sm,
          borderRadius: BORDER_RADIUS.md,
          borderWidth: 1,
          borderColor: '#3A3A3A',
          ...SHADOWS.md,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: ResponsiveSpacing.sm }}>
            <ActivityIndicator size="small" color="#FFFFFF" />
            <Text style={{
              marginLeft: ResponsiveSpacing.sm,
              fontSize: ResponsiveTypography.fontSize.base,
              color: '#FFFFFF',
              fontWeight: '600',
            }}>
              Clearing Data...
            </Text>
          </View>
          {operationProgress.total > 0 && (
            <>
              <View style={{
                backgroundColor: IRACHAT_COLORS.background,
                height: 4,
                borderRadius: 2,
                marginBottom: ResponsiveSpacing.xs,
              }}>
                <View style={{
                  backgroundColor: IRACHAT_COLORS.primary,
                  height: 4,
                  borderRadius: 2,
                  width: `${(operationProgress.current / operationProgress.total) * 100}%`,
                }} />
              </View>
              <Text style={{
                fontSize: ResponsiveTypography.fontSize.sm,
                color: IRACHAT_COLORS.textMuted,
              }}>
                {operationProgress.current} of {operationProgress.total} chats
                {operationProgress.currentChat && ` • ${operationProgress.currentChat}`}
              </Text>
            </>
          )}
        </View>
      )}

      {/* Sync Status Indicator */}
      {syncStatus === 'syncing' && (
        <View style={{
          backgroundColor: 'rgba(16, 185, 129, 0.9)',
          padding: ResponsiveSpacing.sm,
          marginHorizontal: ResponsiveSpacing.lg,
          marginTop: ResponsiveSpacing.sm,
          borderRadius: BORDER_RADIUS.md,
          flexDirection: 'row',
          alignItems: 'center',
        }}>
          <ActivityIndicator size="small" color="#FFFFFF" />
          <Text style={{
            color: '#FFFFFF',
            marginLeft: ResponsiveSpacing.sm,
            fontSize: ResponsiveTypography.fontSize.sm,
            fontWeight: '500',
          }}>
            Syncing changes with server...
          </Text>
        </View>
      )}

      {/* Undo Option */}
      {showUndoOption && lastOperation && (
        <View style={{
          backgroundColor: IRACHAT_COLORS.warning,
          padding: ResponsiveSpacing.md,
          marginHorizontal: ResponsiveSpacing.lg,
          marginTop: ResponsiveSpacing.sm,
          borderRadius: BORDER_RADIUS.md,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
          <View style={{ flex: 1 }}>
            <Text style={{
              fontSize: ResponsiveTypography.fontSize.sm,
              color: IRACHAT_COLORS.text,
              fontWeight: '600',
            }}>
              Cleared {lastOperation.operation} from {lastOperation.chatIds.length} chat(s)
            </Text>
            <Text style={{
              fontSize: ResponsiveTypography.fontSize.xs,
              color: IRACHAT_COLORS.textMuted,
              marginTop: 2,
            }}>
              This action cannot be fully undone
            </Text>
          </View>
          <TouchableOpacity
            onPress={handleUndo}
            style={{
              backgroundColor: IRACHAT_COLORS.surface,
              paddingHorizontal: ResponsiveSpacing.md,
              paddingVertical: ResponsiveSpacing.sm,
              borderRadius: BORDER_RADIUS.sm,
              marginLeft: ResponsiveSpacing.md,
            }}
          >
            <Text style={{
              fontSize: ResponsiveTypography.fontSize.sm,
              color: IRACHAT_COLORS.primary,
              fontWeight: '600',
            }}>
              Info
            </Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Smart Selection Helpers */}
      {selectedChats.size > 0 && (
        <View style={{
          backgroundColor: IRACHAT_COLORS.surface,
          padding: ResponsiveSpacing.md,
          marginHorizontal: ResponsiveSpacing.lg,
          marginTop: ResponsiveSpacing.sm,
          borderRadius: BORDER_RADIUS.md,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
          <Text style={{
            fontSize: ResponsiveTypography.fontSize.sm,
            color: IRACHAT_COLORS.text,
            fontWeight: '600',
          }}>
            {selectedChats.size} chat(s) selected
          </Text>
          <View style={{ flexDirection: 'row', gap: ResponsiveSpacing.xs }}>
            <TouchableOpacity
              onPress={() => selectByType('groups')}
              style={{
                backgroundColor: IRACHAT_COLORS.primary,
                paddingHorizontal: ResponsiveSpacing.sm,
                paddingVertical: ResponsiveSpacing.xs,
                borderRadius: BORDER_RADIUS.sm,
              }}
            >
              <Text style={{
                fontSize: ResponsiveTypography.fontSize.xs,
                color: IRACHAT_COLORS.textOnPrimary,
                fontWeight: '600',
              }}>
                + Groups
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => selectByActivity('inactive')}
              style={{
                backgroundColor: IRACHAT_COLORS.accent,
                paddingHorizontal: ResponsiveSpacing.sm,
                paddingVertical: ResponsiveSpacing.xs,
                borderRadius: BORDER_RADIUS.sm,
              }}
            >
              <Text style={{
                fontSize: ResponsiveTypography.fontSize.xs,
                color: '#FFFFFF',
                fontWeight: '600',
              }}>
                + Inactive
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Chat List */}
      <FlatList
        data={filteredChats}
        renderItem={({ item, index }) => renderChatItem({ item, index })}
        keyExtractor={(item) => item.id}
        style={{ flex: 1 }}
        contentContainerStyle={{ paddingVertical: ResponsiveSpacing.screenPadding, paddingHorizontal: 0 }} // Removed horizontal padding
        showsVerticalScrollIndicator={false}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={10}
        ListEmptyComponent={() => (
          <View style={{
            alignItems: 'center',
            marginTop: 50,
            paddingHorizontal: ResponsiveSpacing.lg,
          }}>
            <Ionicons
              name={searchQuery ? "search" : "chatbubbles"}
              size={64}
              color="#FFFFFF"
            />
            <Text style={{
              fontSize: ResponsiveTypography.fontSize.lg,
              fontWeight: '600',
              color: '#FFFFFF',
              marginTop: ResponsiveSpacing.md,
              textAlign: 'center',
            }}>
              {searchQuery ? 'No matching chats' : 'No chats available'}
            </Text>
            <Text style={{
              fontSize: ResponsiveTypography.fontSize.base,
              color: '#B0B0B0',
              marginTop: ResponsiveSpacing.sm,
              textAlign: 'center',
              lineHeight: 20,
            }}>
              {searchQuery
                ? `Try adjusting your search for "${searchQuery}" or clear filters`
                : 'Start a conversation to see your chats here'
              }
            </Text>
            {searchQuery && (
              <TouchableOpacity
                onPress={() => setSearchQuery('')}
                style={{
                  backgroundColor: '#FFFFFF',
                  paddingHorizontal: ResponsiveSpacing.lg,
                  paddingVertical: ResponsiveSpacing.sm,
                  borderRadius: BORDER_RADIUS.md,
                  marginTop: ResponsiveSpacing.lg,
                }}
              >
                <Text style={{
                  color: '#000000',
                  fontSize: ResponsiveTypography.fontSize.base,
                  fontWeight: '600',
                }}>
                  Clear Search
                </Text>
              </TouchableOpacity>
            )}
          </View>
        )}
      />

      {/* Action Button */}
      {selectedChats.size > 0 && (
        <View style={{
          backgroundColor: '#1A1A1A',
          padding: 20,
          borderTopWidth: 1,
          borderTopColor: '#3A3A3A',
        }}>
          <TouchableOpacity
            onPress={handleClearSelected}
            disabled={clearingInProgress}
            style={{
              backgroundColor: '#DC3545',
              padding: 16,
              borderRadius: 12,
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            {clearingInProgress ? (
              <ActivityIndicator color="white" />
            ) : (
              <>
                <Ionicons name="trash" size={20} color="#FFFFFF" />
                <Text style={{
                  color: '#FFFFFF',
                  fontSize: 16,
                  fontWeight: '600',
                  marginLeft: 8,
                }}>
                  Clear {selectedChats.size} Chat{selectedChats.size > 1 ? 's' : ''}
                </Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      )}

      {/* Clear Options Modal */}
      <Modal
        visible={showClearOptions}
        transparent
        animationType="slide"
        onRequestClose={() => setShowClearOptions(false)}
      >
        <View style={{
          flex: 1,
          backgroundColor: "rgba(0,0,0,0.5)",
          justifyContent: "center",
          alignItems: "center",
          padding: ResponsiveSpacing.screenPadding,
        }}>
          <ResponsiveCard
            variant="elevated"
            style={{
              width: "100%",
              maxWidth: 400,
            }}
          >
            <Text style={{
              fontSize: 20,
              fontWeight: "bold",
              textAlign: "center",
              marginBottom: 20,
              color: "#333",
            }}>
              Clear Chat Data
            </Text>

            <Text style={{
              fontSize: 16,
              color: "#666",
              textAlign: "center",
              marginBottom: 20,
            }}>
              What would you like to clear from the selected {selectedChats.size} chat{selectedChats.size > 1 ? 's' : ''}?
            </Text>

            <TouchableOpacity
              onPress={() => {
                Alert.alert(
                  'Clear Messages Only',
                  'This will delete all messages but keep media files. Continue?',
                  [
                    { text: 'Cancel', style: 'cancel' },
                    { text: 'Clear Messages', style: 'destructive', onPress: () => executeClearOperation('messages') }
                  ]
                );
              }}
              style={{
                backgroundColor: '#F59E0B',
                padding: 16,
                borderRadius: 12,
                marginBottom: 12,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Ionicons name="chatbubble" size={20} color="#FFFFFF" />
              <Text style={{
                color: '#FFFFFF',
                fontSize: 16,
                fontWeight: '600',
                marginLeft: 8,
              }}>
                Clear Messages Only
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => {
                Alert.alert(
                  'Clear Media Only',
                  'This will delete all media files but keep text messages. Continue?',
                  [
                    { text: 'Cancel', style: 'cancel' },
                    { text: 'Clear Media', style: 'destructive', onPress: () => executeClearOperation('media') }
                  ]
                );
              }}
              style={{
                backgroundColor: '#10B981',
                padding: 16,
                borderRadius: 12,
                marginBottom: 12,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Ionicons name="images" size={20} color="#FFFFFF" />
              <Text style={{
                color: '#FFFFFF',
                fontSize: 16,
                fontWeight: '600',
                marginLeft: 8,
              }}>
                Clear Media Only
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => {
                Alert.alert(
                  'Clear Everything',
                  'This will permanently delete ALL messages and media from the selected chats. This action cannot be undone. Continue?',
                  [
                    { text: 'Cancel', style: 'cancel' },
                    { text: 'Clear Everything', style: 'destructive', onPress: () => executeClearOperation('all') }
                  ]
                );
              }}
              style={{
                backgroundColor: '#DC3545',
                padding: 16,
                borderRadius: 12,
                marginBottom: 20,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Ionicons name="trash" size={20} color="#FFFFFF" />
              <Text style={{
                color: '#FFFFFF',
                fontSize: 16,
                fontWeight: '600',
                marginLeft: 8,
              }}>
                Clear Everything
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => setShowClearOptions(false)}
              style={{
                backgroundColor: '#f0f0f0',
                padding: 16,
                borderRadius: 12,
              }}
            >
              <Text style={{
                textAlign: "center",
                fontSize: 16,
                fontWeight: "600",
                color: "#666",
              }}>
                Cancel
              </Text>
            </TouchableOpacity>
          </ResponsiveCard>
        </View>
      </Modal>

      {/* Floating Action Button for Quick Actions */}
      <FloatingActionButton
        actions={[
          {
            ...QuickNavActions.newChat,
            backgroundColor: IRACHAT_COLORS.primary,
            color: IRACHAT_COLORS.textOnPrimary,
          },
          {
            ...QuickNavActions.createGroup,
            backgroundColor: IRACHAT_COLORS.success,
            color: IRACHAT_COLORS.textOnPrimary,
          },
          {
            ...QuickNavActions.contacts,
            backgroundColor: IRACHAT_COLORS.secondary,
            color: IRACHAT_COLORS.textOnPrimary,
          },
          {
            ...QuickNavActions.search,
            backgroundColor: IRACHAT_COLORS.warning,
            color: IRACHAT_COLORS.textOnPrimary,
          },
        ]}
        mainAction={{
          icon: 'add',
          onPress: () => navigationService.openNewChat(),
          backgroundColor: IRACHAT_COLORS.primary,
        }}
      />
    </ResponsiveContainer>
    </Animated.View>
  );
}
