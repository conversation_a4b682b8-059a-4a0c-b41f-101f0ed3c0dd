// 🚀 COMPREHENSIVE UPDATES SCREEN
// Complete TikTok-style social media updates with advanced features

// Declare global types for React Native environment
declare const window: any;

import React, { useEffect, useState, useRef, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  RefreshControl,
  StatusBar,
  ScrollView,
  Image,
  Animated,
  TextInput,
  SafeAreaView,
} from 'react-native';
// Removed unused gesture imports - now using horizontal FlatList
import { Ionicons } from '@expo/vector-icons';
import { useSelector } from 'react-redux';
import { RootState } from '../../src/redux/store';
import { comprehensiveUpdatesService } from '../../src/services/comprehensiveUpdatesService';
import { localUpdatesStorage } from '../../src/services/localUpdatesStorage';
import { updatesSyncService } from '../../src/services/updatesSyncService';

// Safety wrapper to handle potential import issues
const safeUpdatesSyncService = {
  startBackgroundSync: () => {
    try {
      if (updatesSyncService && typeof updatesSyncService.startBackgroundSync === 'function') {
        return updatesSyncService.startBackgroundSync();
      }
    } catch (error) {
      console.error('Error starting background sync:', error);
    }
  },
  stopBackgroundSync: () => {
    try {
      if (updatesSyncService && typeof updatesSyncService.stopBackgroundSync === 'function') {
        return updatesSyncService.stopBackgroundSync();
      }
    } catch (error) {
      console.error('Error stopping background sync:', error);
    }
  },
  forceSyncAll: async () => {
    try {
      if (updatesSyncService && typeof updatesSyncService.forceSyncAll === 'function') {
        return await updatesSyncService.forceSyncAll();
      }
      return { synced: 0, failed: 0 };
    } catch (error) {
      console.error('Error in forceSyncAll:', error);
      return { synced: 0, failed: 0 };
    }
  }
};
import { navigationService, ROUTES } from '../../src/services/navigationService';
import { Update, Story, CreateUpdateData, UpdateType, FeedConfig } from '../../src/types/Update';
import { Video, ResizeMode } from 'expo-av';
import * as ImagePicker from 'expo-image-picker';

import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';
import NetInfo from '@react-native-community/netinfo';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// IraChat Branding Colors
const COLORS = {
  primary: '#87CEEB',      // Sky Blue - IraChat's primary brand color
  primaryDark: '#4682B4',  // Steel Blue - darker variant
  primaryLight: '#B0E0E6', // Powder Blue - lighter variant
  secondary: '#1E90FF',    // Dodger Blue - accent color
  background: '#000000',   // Pure Black
  surface: '#1A1A1A',     // Dark Gray
  surfaceLight: '#2A2A2A', // Lighter Gray
  text: '#FFFFFF',         // White text
  textSecondary: '#B0B0B0', // Gray text
  textMuted: '#808080',    // Muted text
  success: '#00FF7F',      // Spring Green
  warning: '#FFD700',      // Gold
  error: '#FF6B6B',        // Light Red
  gradient: ['#87CEEB', '#4682B4', '#1E90FF'], // Sky blue gradient
  overlay: 'rgba(0, 0, 0, 0.7)',
};

interface ComprehensiveUpdatesScreenProps {
  initialTab?: 'feed' | 'stories' | 'live' | 'trending';
}

export default function ComprehensiveUpdatesScreen({
  initialTab = 'feed'
}: ComprehensiveUpdatesScreenProps): React.JSX.Element {
  const currentUser = useSelector((state: RootState) => state.user?.currentUser);
  const insets = useSafeAreaInsets();

  // Responsive dimensions
  const isTablet = SCREEN_WIDTH > 768;
  const isLandscape = SCREEN_WIDTH > SCREEN_HEIGHT;
  const responsiveWidth = isTablet ? Math.min(SCREEN_WIDTH * 0.8, 600) : SCREEN_WIDTH;
  const storySize = isTablet ? 80 : 60;
  const headerHeight = 60 + insets.top;

  // Create responsive styles
  const getStyles = () => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: COLORS.background,
      width: responsiveWidth,
    },
    header: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      height: headerHeight,
      backgroundColor: 'rgba(18, 18, 18, 0.95)', // Increased opacity to compensate for removed backdrop filter
      paddingTop: insets.top,
      paddingHorizontal: 16,
      justifyContent: 'center',
      zIndex: 1000,
    },
    headerContent: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      height: 44,
    },
    headerLeft: {
      flex: 1,
    },
    headerRight: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 12,
    },
    headerIcon: {
      padding: 8,
      borderRadius: 20,
      backgroundColor: 'transparent',
    },
    headerAvatar: {
      width: 24,
      height: 24,
      borderRadius: 12,
    },
    tabBar: {
      position: 'absolute',
      top: headerHeight,
      left: 0,
      right: 0,
      flexDirection: 'column',
      backgroundColor: 'rgba(18, 18, 18, 0.95)', // More opaque for better visibility
      paddingHorizontal: 16,
      paddingTop: 8,
      paddingBottom: 8, // Increased bottom padding for indicator space
      zIndex: 999,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.3,
      shadowRadius: 4,
      elevation: 8,
    },
    tabBarContent: {
      flexDirection: 'row',
      position: 'relative',
      height: 44, // Fixed height for consistent positioning
    },
    tabItem: {
      flex: 1,
      paddingVertical: 12,
      alignItems: 'center',
      borderRadius: 8,
      marginHorizontal: 2,
      position: 'relative',
    },
    activeTabItem: {
      // Remove background color as we'll use the animated indicator
    },
    tabText: {
      color: COLORS.textSecondary,
      fontSize: isTablet ? 18 : 16,
      fontWeight: '500',
    },
    activeTabText: {
      color: COLORS.primary,
      fontWeight: '700',
    },
    tabIndicator: {
      position: 'absolute',
      bottom: 2, // Small margin from bottom
      height: 3,
      width: 60,
      backgroundColor: COLORS.primary,
      borderRadius: 2,
      shadowColor: COLORS.primary,
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.8,
      shadowRadius: 4,
      elevation: 4,
      left: 0, // Will be animated via transform
    },
    tabRipple: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: 8,
      backgroundColor: 'rgba(135, 206, 235, 0.1)',
      opacity: 0,
    },
    storyContainer: {
      alignItems: 'center',
      marginHorizontal: isTablet ? 12 : 10,
      width: storySize,
    },
    storyImage: {
      width: storySize - 16,
      height: storySize - 16,
      borderRadius: (storySize - 16) / 2,
    },
    content: {
      flex: 1,
      flexDirection: isLandscape && isTablet ? 'row' : 'column',
      backgroundColor: COLORS.background, // Consistent background color
      // No padding - content extends to full screen behind floating overlays
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: COLORS.background, // Consistent background color
      paddingTop: headerHeight, // Only account for header, content flows behind tab bar
    },
    loadingText: {
      marginTop: 16,
      color: COLORS.textSecondary,
      fontSize: 16,
    },
    storiesContainer: {
      height: 120 + headerHeight + 48, // Add space for floating header + tab bar
      backgroundColor: COLORS.surface,
      paddingTop: headerHeight + 48 + 12, // Account for header + tab bar space for stories row
      paddingBottom: 12,
      paddingHorizontal: 8,
      // Remove border since content flows behind tab bar
    },
    myStoryContainer: {
      alignItems: 'center',
      marginHorizontal: 10,
      width: storySize,
    },
    myStoryImageContainer: {
      position: 'relative',
    },
    storyImageContainer: {
      borderWidth: 3,
      borderColor: COLORS.primary,
      borderRadius: storySize / 2,
      padding: 3,
      shadowColor: COLORS.primary,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.3,
      shadowRadius: 4,
      elevation: 4,
    },
    viewedStory: {
      borderColor: COLORS.textMuted,
      shadowColor: COLORS.textMuted,
    },
    defaultAvatar: {
      backgroundColor: COLORS.surfaceLight,
      justifyContent: 'center',
      alignItems: 'center',
    },
    addStoryButton: {
      position: 'absolute',
      bottom: -2,
      right: -2,
      backgroundColor: COLORS.primary,
      borderRadius: 12,
      width: 24,
      height: 24,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 2,
      borderColor: COLORS.surface,
    },
    storyText: {
      marginTop: 4,
      fontSize: 12,
      color: COLORS.textSecondary,
      textAlign: 'center',
    },
    storyLabel: {
      marginTop: 4,
      fontSize: 12,
      color: COLORS.textSecondary,
      textAlign: 'center',
      maxWidth: storySize,
    },

    // Floating Stories Overlay Styles
    floatingStoriesContainer: {
      position: 'absolute',
      top: headerHeight + 48 + 12, // Position below tab bar with 12px padding
      left: 0,
      right: 0,
      height: 100,
      backgroundColor: 'transparent', // Completely transparent - no background
      paddingVertical: 8,
      paddingHorizontal: 8,
      zIndex: 997, // Below tab bar but above content
      // Remove backdrop filter to make it truly transparent
    },
    floatingMyStoryContainer: {
      alignItems: 'center',
      marginHorizontal: 6,
      width: storySize,
      // Add shadow and background to make it float above content
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.3,
      shadowRadius: 4,
      elevation: 5, // Android shadow
    },
    floatingStoryContainer: {
      alignItems: 'center',
      marginHorizontal: 6,
      width: storySize,
      // Add shadow and background to make it float above content
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.3,
      shadowRadius: 4,
      elevation: 5, // Android shadow
    },
    floatingMyStoryImageContainer: {
      position: 'relative',
    },
    floatingStoryImageContainer: {
      width: storySize - 8,
      height: storySize - 8,
      borderRadius: (storySize - 8) / 2,
      borderWidth: 2,
      borderColor: COLORS.primary,
      padding: 2,
      backgroundColor: COLORS.surface, // Add background to make it stand out
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.2,
      shadowRadius: 2,
      elevation: 3,
    },
    floatingStoryImage: {
      width: storySize - 12,
      height: storySize - 12,
      borderRadius: (storySize - 12) / 2,
    },
    floatingDefaultAvatar: {
      backgroundColor: COLORS.surfaceLight,
      justifyContent: 'center',
      alignItems: 'center',
    },
    floatingAddStoryButton: {
      position: 'absolute',
      bottom: -2,
      right: -2,
      width: 16,
      height: 16,
      borderRadius: 8,
      backgroundColor: COLORS.primary,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: '#FFFFFF',
    },
    floatingStoryLabel: {
      marginTop: 4,
      fontSize: 10,
      color: COLORS.text,
      textAlign: 'center',
      maxWidth: storySize - 8,
      backgroundColor: 'rgba(0, 0, 0, 0.6)', // Semi-transparent background for readability
      paddingHorizontal: 6,
      paddingVertical: 2,
      borderRadius: 8,
      overflow: 'hidden',
    },
    floatingViewedStory: {
      borderColor: COLORS.textMuted,
      opacity: 0.6,
    },
    headerTitle: {
      fontSize: isTablet ? 24 : 20,
      fontWeight: 'bold',
      color: COLORS.text,
    },
    updatesList: {
      flex: 1,
      // No padding here since stories container handles the top spacing
    },
    updatesListBehindTabs: {
      flex: 1,
      marginTop: -(48), // Negative margin to flow behind tab bar
    },
    // Removed FAB styles - functionality moved to header avatar
    titleContainer: {
      flexDirection: 'column',
      alignItems: 'flex-start',
    },
    syncStatusContainer: {
      marginTop: 2,
      minHeight: 16,
    },
    syncIndicator: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
    },
    syncText: {
      fontSize: 10,
      color: COLORS.textMuted,
      fontWeight: '500',
    },

    // Stories Tab Styles
    storiesScrollView: {
      flex: 1,
      backgroundColor: COLORS.background, // Consistent background color
      paddingTop: headerHeight, // Only account for header, content flows behind tab bar
    },
    myStoriesSection: {
      padding: 16,
    },
    friendsStoriesSection: {
      padding: 16,
      paddingTop: 0,
    },
    sectionTitle: {
      fontSize: 20,
      fontWeight: '700',
      color: COLORS.text,
      marginBottom: 16,
    },
    storiesGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
    },
    addStoryCard: {
      width: (responsiveWidth - 48) / 2,
      height: 200,
      backgroundColor: COLORS.surfaceLight,
      borderRadius: 12,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 16,
      borderWidth: 2,
      borderColor: COLORS.primary,
      borderStyle: 'dashed',
    },
    addStoryIcon: {
      width: 60,
      height: 60,
      borderRadius: 30,
      backgroundColor: COLORS.primary + '20',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 8,
    },
    addStoryText: {
      fontSize: 16,
      fontWeight: '600',
      color: COLORS.primary,
    },
    storyGridItem: {
      width: (responsiveWidth - 48) / 2,
      height: 200,
      borderRadius: 12,
      overflow: 'hidden',
      marginBottom: 16,
      position: 'relative',
    },
    storyGridImage: {
      width: '100%',
      height: '100%',
      resizeMode: 'cover',
    },
    storyGridOverlay: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      backgroundColor: 'rgba(0,0,0,0.6)',
      padding: 8,
    },
    storyGridUser: {
      color: '#FFFFFF',
      fontSize: 14,
      fontWeight: '600',
      marginBottom: 2,
    },
    storyGridTime: {
      color: '#FFFFFF',
      fontSize: 12,
      opacity: 0.8,
    },
    unviewedIndicator: {
      position: 'absolute',
      top: 8,
      right: 8,
      width: 12,
      height: 12,
      borderRadius: 6,
      backgroundColor: COLORS.primary,
    },

    // Live Tab Styles
    liveStreamsList: {
      flex: 1,
      backgroundColor: COLORS.background, // Consistent background color
      paddingTop: headerHeight, // Only account for header, content flows behind tab bar
    },
    liveStreamCard: {
      backgroundColor: COLORS.surface,
      margin: 16,
      borderRadius: 12,
      overflow: 'hidden',
    },
    liveStreamHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 12,
      backgroundColor: 'rgba(255, 0, 0, 0.1)',
    },
    liveIndicator: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    liveDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: '#FF0000',
      marginRight: 6,
    },
    liveText: {
      color: '#FF0000',
      fontSize: 12,
      fontWeight: '700',
    },
    viewerCount: {
      color: COLORS.textMuted,
      fontSize: 12,
    },
    startLiveButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: COLORS.primary,
      paddingHorizontal: 24,
      paddingVertical: 12,
      borderRadius: 20,
      marginTop: 16,
    },
    startLiveButtonText: {
      color: '#FFFFFF',
      fontSize: 16,
      fontWeight: '600',
      marginLeft: 8,
    },

    // Trending Tab Styles
    trendingList: {
      flex: 1,
      backgroundColor: COLORS.background, // Consistent background color
      paddingTop: headerHeight, // Only account for header, content flows behind tab bar
    },
    trendingCard: {
      backgroundColor: COLORS.surface,
      margin: 16,
      borderRadius: 12,
      overflow: 'hidden',
    },
    trendingHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 12,
      backgroundColor: 'rgba(255, 107, 107, 0.1)',
    },
    trendingBadge: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    trendingText: {
      color: '#FF6B6B',
      fontSize: 12,
      fontWeight: '700',
      marginLeft: 4,
    },
    engagementMetrics: {
      alignItems: 'flex-end',
    },
    engagementText: {
      color: COLORS.textMuted,
      fontSize: 12,
    },

    // Empty States
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 32,
      paddingTop: headerHeight, // Only account for header, content flows behind tab bar
      backgroundColor: COLORS.background, // Consistent background color
    },
    emptyTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: COLORS.text,
      marginTop: 16,
      marginBottom: 8,
    },
    emptySubtitle: {
      fontSize: 16,
      color: COLORS.textMuted,
      textAlign: 'center',
      lineHeight: 24,
    },
    emptyStoriesContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 32,
      paddingVertical: 64,
    },
    emptyStoriesText: {
      fontSize: 20,
      fontWeight: '600',
      color: COLORS.text,
      marginTop: 16,
      marginBottom: 8,
    },
    emptyStoriesSubtext: {
      fontSize: 16,
      color: COLORS.textMuted,
      textAlign: 'center',
      lineHeight: 24,
      marginBottom: 32,
    },
    createStoryButton: {
      backgroundColor: COLORS.primary,
      paddingHorizontal: 24,
      paddingVertical: 12,
      borderRadius: 20,
    },
    createStoryButtonText: {
      color: '#FFFFFF',
      fontSize: 16,
      fontWeight: '600',
    },
  });

  const styles = getStyles();

  // ==================== HELPER FUNCTIONS ====================

  // Format time ago for stories
  const formatTimeAgo = (timestamp: Date): string => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));

    if (hours < 1) {
      const minutes = Math.floor(diff / (1000 * 60));
      return `${minutes}m ago`;
    } else if (hours < 24) {
      return `${hours}h ago`;
    } else {
      return `${Math.floor(hours / 24)}d ago`;
    }
  };

  // ==================== STATE MANAGEMENT ====================
  
  // Core State
  const [activeTab, setActiveTab] = useState<'feed' | 'stories' | 'live' | 'trending'>(initialTab);
  const [updates, setUpdates] = useState<Update[]>([]);
  const [stories, setStories] = useState<Story[]>([]);
  const [myStories, setMyStories] = useState<Story[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [nextCursor, setNextCursor] = useState<string>();

  // Network and Sync State
  const [isOnline, setIsOnline] = useState(true);
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'error'>('idle');
  const [pendingUpdatesCount, setPendingUpdatesCount] = useState(0);
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);

  // Creation State
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showStoryCreator, setShowStoryCreator] = useState(false);
  const [showCameraCapture, setShowCameraCapture] = useState(false);
  const [creationMode, setCreationMode] = useState<'update' | 'story'>('update');
  const [showCaptionModal, setShowCaptionModal] = useState(false);
  const [captionText, setCaptionText] = useState('');
  const [selectedMediaUri, setSelectedMediaUri] = useState<string>('');
  const [selectedMediaType, setSelectedMediaType] = useState<UpdateType>('image');
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);

  // Interaction State
  const [showInteractionModal, setShowInteractionModal] = useState(false);
  const [selectedUpdateId, setSelectedUpdateId] = useState<string>('');
  const [interactionType, setInteractionType] = useState<'likes' | 'views' | 'shares' | 'comments'>('likes');

  // Story Viewer State
  const [showStoryViewer, setShowStoryViewer] = useState(false);
  const [currentStoryIndex, setCurrentStoryIndex] = useState(0);
  const [storyViewMode, setStoryViewMode] = useState<'my' | 'friends'>('friends');
  const [showAvatarMenu, setShowAvatarMenu] = useState(false);
  const [showTextStoryModal, setShowTextStoryModal] = useState(false);

  // Feed Configuration
  const [feedConfig] = useState<FeedConfig>({
    algorithm: 'personalized',
    includeStories: false,
    includeFriends: true,
    includeFollowing: true,
    includePublic: true,
    contentTypes: ['image', 'video', 'text'],
    maxAge: 168, // 7 days
    minEngagement: 0,
  });



  // ==================== HORIZONTAL SWIPE TAB NAVIGATION ====================

  const tabs = ['feed', 'stories', 'live', 'trending'] as const;

  // Animation values for horizontal scrolling like updates page
  const scrollX = useRef(new Animated.Value(0)).current;
  const tabIndicatorX = useRef(new Animated.Value(0)).current;
  const flatListRef = useRef<FlatList>(null);

  // Calculate tab indicator position
  const getTabIndicatorPosition = (tabIndex: number) => {
    const tabWidth = (SCREEN_WIDTH - 32) / tabs.length; // Account for horizontal padding
    const indicatorWidth = 60;
    return tabWidth * tabIndex + (tabWidth - indicatorWidth) / 2;
  };

  // Removed stories overlay - redundant with header avatar

  // Handle scroll events from the horizontal FlatList with debouncing
  const [isScrolling, setIsScrolling] = useState(false);

  // Animate tab indicator when tab changes (only for programmatic changes, not during user scrolling)
  useEffect(() => {
    if (isScrolling) return; // Don't animate during user scrolling

    const currentIndex = tabs.indexOf(activeTab);
    const targetPosition = getTabIndicatorPosition(currentIndex);

    Animated.spring(tabIndicatorX, {
      toValue: targetPosition,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();

    // Removed stories overlay logic - no longer needed

    // Scroll to the active tab content (only if not caused by user swiping)
    if (flatListRef.current && !isUserSwiping.current) {
      flatListRef.current.scrollToIndex({
        index: currentIndex,
        animated: true,
      });
    }
  }, [activeTab, isScrolling]);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastTabChangeRef = useRef<number>(0);
  const isUserSwiping = useRef<boolean>(false);

  const handleScroll = Animated.event(
    [{ nativeEvent: { contentOffset: { x: scrollX } } }],
    {
      useNativeDriver: false,
      listener: (event: any) => {
        const offsetX = event.nativeEvent.contentOffset.x;
        const currentIndex = Math.round(offsetX / SCREEN_WIDTH);

        // Smooth tab indicator animation based on scroll position
        const progress = offsetX / SCREEN_WIDTH;
        const tabWidth = (SCREEN_WIDTH - 32) / tabs.length; // Account for horizontal padding
        const indicatorWidth = 60;
        const indicatorPosition = (tabWidth * progress) + (tabWidth - indicatorWidth) / 2;

        // Update indicator position smoothly during scroll
        tabIndicatorX.setValue(indicatorPosition);

        // Debounce tab changes to prevent infinite loops (reduced cooldown for better responsiveness)
        const now = Date.now();
        if (now - lastTabChangeRef.current < 200) return; // 200ms cooldown (reduced from 500ms)

        // Update active tab based on scroll position
        if (currentIndex >= 0 && currentIndex < tabs.length && tabs[currentIndex] !== activeTab && !isScrolling) {
          lastTabChangeRef.current = now;
          isUserSwiping.current = true; // Mark as user-initiated change
          setActiveTab(tabs[currentIndex]);

          // Reset the flag after a short delay
          setTimeout(() => {
            isUserSwiping.current = false;
          }, 100);

          // Haptic feedback on tab change
          try {
            const { HapticFeedback } = require('expo-haptics');
            HapticFeedback.impactAsync(HapticFeedback.ImpactFeedbackStyle.Light);
          } catch (error) {
            // Haptic feedback not available
          }
        }
      }
    }
  );

  // Handle momentum scroll end to snap to nearest tab
  const handleMomentumScrollEnd = (event: any) => {
    const offsetX = event.nativeEvent.contentOffset.x;
    const currentIndex = Math.round(offsetX / SCREEN_WIDTH);

    setIsScrolling(false);

    // Only update if different and enough time has passed (reduced cooldown)
    const now = Date.now();
    if (currentIndex >= 0 && currentIndex < tabs.length && tabs[currentIndex] !== activeTab && now - lastTabChangeRef.current >= 200) {
      lastTabChangeRef.current = now;
      isUserSwiping.current = true; // Mark as user-initiated change
      setActiveTab(tabs[currentIndex]);

      // Reset the flag after a short delay
      setTimeout(() => {
        isUserSwiping.current = false;
      }, 100);
    }
  };

  // Handle scroll begin to prevent tab changes during scroll
  const handleScrollBeginDrag = () => {
    setIsScrolling(true);
    isUserSwiping.current = true; // User started swiping
  };

  // Format sync time for display
  const formatSyncTime = useCallback((time: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - time.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));

    if (diffMinutes < 1) return 'just now';
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    return time.toLocaleDateString();
  }, []);

  // Enhanced sync status monitoring
  const updateSyncStatus = useCallback(async () => {
    try {
      const pendingUpdates = await localUpdatesStorage.getPendingSyncUpdates();
      setPendingUpdatesCount(pendingUpdates.length);

      // If we have pending updates and we're online, try to sync
      if (pendingUpdates.length > 0 && isOnline && syncStatus === 'idle') {
        setSyncStatus('syncing');
        try {
          const syncResult = await safeUpdatesSyncService.forceSyncAll();
          setPendingUpdatesCount(syncResult.failed);
          setLastSyncTime(new Date());
          setSyncStatus('idle');
        } catch (error) {
          setSyncStatus('error');
        }
      }
    } catch (error) {
      setSyncStatus('error');
    }
  }, [isOnline, syncStatus]);

  // Periodic sync status check
  useEffect(() => {
    const interval = setInterval(updateSyncStatus, 10000); // Check every 10 seconds
    return () => clearInterval(interval);
  }, [updateSyncStatus]);

  // Animation Values
  const scrollY = useRef(new Animated.Value(0)).current;
  const headerOpacity = useRef(new Animated.Value(1)).current;
  // Removed fabScale - FAB functionality moved to header avatar

  // ==================== LIFECYCLE METHODS ====================

  // Real-time listeners refs
  const updatesListenerRef = useRef<(() => void) | null>(null);
  const storiesListenerRef = useRef<(() => void) | null>(null);

  // Initialize local storage and database with enhanced sync monitoring
  useEffect(() => {
    const initializeLocalStorage = async () => {
      try {
        // Initialize database first to prevent NullPointerException
        await localUpdatesStorage.initialize();

        // Clear expired stories on app start
        await localUpdatesStorage.clearExpiredStories();

        // Start background sync service
        safeUpdatesSyncService.startBackgroundSync();

        // Check initial network connectivity
        const networkState = await NetInfo.fetch();
        setIsOnline(networkState.isConnected ?? false);

        if (networkState.isConnected) {
          setSyncStatus('syncing');
          try {
            // Force sync pending updates when online
            const syncResult = await safeUpdatesSyncService.forceSyncAll();
            setPendingUpdatesCount(syncResult.failed);
            setLastSyncTime(new Date());
            setSyncStatus('idle');
          } catch (error) {
            setSyncStatus('error');
          }
        }

        // Listen for network state changes with enhanced logic
        const unsubscribe = NetInfo.addEventListener(async (state) => {
          const wasOffline = !isOnline;
          const isNowOnline = state.isConnected ?? false;
          setIsOnline(isNowOnline);

          if (wasOffline && isNowOnline && currentUser?.id) {
            // When coming back online, sync pending updates
            setSyncStatus('syncing');
            try {
              const syncResult = await safeUpdatesSyncService.forceSyncAll();
              setPendingUpdatesCount(syncResult.failed);
              setLastSyncTime(new Date());
              setSyncStatus('idle');

              // Refresh the current tab content
              if (activeTab === 'feed') {
                await Promise.all([
                  loadUpdatesFeed(true),
                  loadStoriesFeed()
                ]);
              } else if (activeTab === 'stories') {
                await loadStoriesFeed();
              } else if (activeTab === 'live') {
                await loadLiveStreams();
              } else if (activeTab === 'trending') {
                await loadTrendingContent();
              }
            } catch (error) {
              setSyncStatus('error');
            }
          }
        });

        return unsubscribe;
      } catch (error) {
        setSyncStatus('error');
        Alert.alert('Database Error', 'Failed to initialize local storage. Please restart the app.');
      }
    };

    let networkUnsubscribe: (() => void) | undefined;

    initializeLocalStorage().then(unsubscribe => {
      networkUnsubscribe = unsubscribe;
    });

    // Cleanup on unmount
    return () => {
      safeUpdatesSyncService.stopBackgroundSync();

      if (networkUnsubscribe) {
        try {
          networkUnsubscribe();
        } catch (error) {
          console.error('Error unsubscribing from network:', error);
        }
      }
    };
  }, [currentUser?.id]);

  // Load data when tab changes (with guards to prevent infinite loops)
  const [tabDataLoaded, setTabDataLoaded] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (!currentUser?.id || isLoading) return;

    // Prevent infinite loops by tracking which tabs have been loaded
    if (tabDataLoaded.has(activeTab)) return;

    const loadTabData = async () => {
      try {
        switch (activeTab) {
          case 'live':
            if (updates.length === 0) {
              await loadLiveStreams();
              setTabDataLoaded(prev => new Set(prev).add('live'));
            }
            break;
          case 'trending':
            if (updates.length === 0) {
              await loadTrendingContent();
              setTabDataLoaded(prev => new Set(prev).add('trending'));
            }
            break;
          case 'stories':
            if (stories.length === 0 && myStories.length === 0) {
              await loadStoriesFeed();
              setTabDataLoaded(prev => new Set(prev).add('stories'));
            }
            break;
          // Feed tab data is loaded in initialization
        }
      } catch (error) {
        console.error(`Error loading ${activeTab} data:`, error);
        // Don't mark as loaded if there was an error, but add a delay to prevent immediate retry
        setTimeout(() => {
          setTabDataLoaded(prev => new Set(prev).add(activeTab));
        }, 5000); // 5 second delay before allowing retry
      }
    };

    loadTabData();
  }, [activeTab, currentUser?.id, isLoading, tabDataLoaded]);

  // ==================== CORE FUNCTIONS ====================

  const initializeScreen = useCallback(async () => {
    if (!currentUser?.id) {
      return;
    }

    setIsLoading(true);

    try {
      // Load initial data based on active tab
      if (activeTab === 'feed') {
        await loadUpdatesFeed(true);
        await loadStoriesFeed();
      } else if (activeTab === 'stories') {
        await loadStoriesFeed();
      } else if (activeTab === 'live') {
        await loadLiveStreams();
      } else if (activeTab === 'trending') {
        await loadTrendingContent();
      }

      // Setup real-time listeners for updates and stories
      await Promise.all([
        setupUpdatesListener(),
        setupStoriesListener(),
      ]);

      // Use StatusBar for better mobile experience
      StatusBar.setBarStyle('light-content', true);
    } catch (error) {
      Alert.alert('Error', 'Failed to load content. Please check your connection.');
    } finally {
      setIsLoading(false);
    }
  }, [currentUser?.id, feedConfig]);

  // Setup real-time listener for updates feed
  const setupUpdatesListener = useCallback(async () => {
    if (!currentUser?.id) return;

    try {
      // Clean up existing listener
      if (updatesListenerRef.current) {
        updatesListenerRef.current();
        updatesListenerRef.current = null;
      }

      // Get initial updates feed from local storage first (offline-first)
      const localUpdates = await localUpdatesStorage.getUpdates(20, 0, feedConfig.includeStories);
      if (localUpdates.length > 0) {
        setUpdates(localUpdates);
        setHasMore(localUpdates.length >= 20);
      }

      // Then try to sync with Firebase
      const result = await comprehensiveUpdatesService.getUpdatesFeed(
        currentUser.id,
        feedConfig,
        undefined,
        20
      );

      if (result.success && result.data) {
        setUpdates(result.data.updates);
        setHasMore(result.data.hasMore);
        setNextCursor(result.data.nextCursor);
      }

      // Setup real-time listener for updates using Firebase onSnapshot
      try {
        const { onSnapshot, query, collection, where, orderBy, limit } = await import('firebase/firestore');
        const { db } = await import('../../src/services/firebaseSimple');

        if (!db) {
          // Firebase not available, use offline mode only
          return;
        }

        // Simplified query to avoid index requirement - filter privacy client-side
        const updatesQuery = query(
          collection(db, 'updates'),
          where('isVisible', '==', true),
          orderBy('timestamp', 'desc'),
          limit(100) // Get more to account for client-side filtering
        );

        updatesListenerRef.current = onSnapshot(
          updatesQuery,
          async (snapshot) => {
            const realtimeUpdates: Update[] = [];
            const now = new Date();

            // Process each document with enhanced logic
            for (const doc of snapshot.docs) {
              const data = doc.data();

              // Enhanced update object with proper data validation
              const update: Update = {
                id: doc.id,
                ...data,
                timestamp: data.timestamp?.toDate() || now,
                expiresAt: data.expiresAt?.toDate(),
                isLikedByCurrentUser: data.likes?.includes(currentUser.id) || false,
                isViewedByCurrentUser: data.views?.some((v: any) => v.userId === currentUser.id) || false,
                // Ensure all required fields are present
                likeCount: data.likeCount || 0,
                commentCount: data.commentCount || 0,
                shareCount: data.shareCount || 0,
                viewCount: data.viewCount || 0,
                media: data.media || [],
                hashtags: data.hashtags || [],
                mentions: data.mentions || [],
                groupTags: data.groupTags || [],
              } as Update;

              // Filter out expired stories
              if (update.isStory && update.expiresAt && update.expiresAt < now) {
                continue;
              }

              // Client-side privacy filtering (since we removed it from the query)
              if (!['public', 'friends'].includes(update.privacy)) {
                continue;
              }

              // Validate update data integrity
              if (update.userId && update.userName && update.type) {
                realtimeUpdates.push(update);
              }
            }

            // Save to local storage for offline access with conflict resolution
            for (const update of realtimeUpdates) {
              try {
                // Check if local version exists and compare timestamps
                const localUpdate = await localUpdatesStorage.getUpdateById(update.id);
                if (!localUpdate || new Date(localUpdate.timestamp).getTime() < update.timestamp.getTime()) {
                  await localUpdatesStorage.saveUpdate(update);
                }
              } catch (storageError) {
                // Continue if storage fails but log for debugging
              }
            }

            // Sort by timestamp for consistent ordering
            realtimeUpdates.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

            setUpdates(realtimeUpdates);
            setHasMore(realtimeUpdates.length >= 50);
            setLastSyncTime(new Date());
          },
          async (error) => {
            // Log error for debugging and fallback to local storage
            if (error.code === 'permission-denied') {
              Alert.alert('Permission Error', 'Unable to access real-time updates');
            }
            const fallbackUpdates = await localUpdatesStorage.getUpdates(20, 0, feedConfig.includeStories);
            setUpdates(fallbackUpdates);
            setHasMore(fallbackUpdates.length >= 20);
          }
        );
      } catch (firebaseError) {
        // Firebase not available, continue with offline mode
        Alert.alert('Offline Mode', 'Real-time updates unavailable. Using offline data.');
      }

    } catch (error) {
      Alert.alert('Error', 'Failed to setup updates listener.');
    }
  }, [currentUser?.id, feedConfig]);

  // Setup real-time listener for stories
  const setupStoriesListener = useCallback(async () => {
    if (!currentUser?.id) return;

    try {
      // Clean up existing listener
      if (storiesListenerRef.current) {
        storiesListenerRef.current();
        storiesListenerRef.current = null;
      }

      // Get initial stories from local storage first (offline-first)
      const localStories = await localUpdatesStorage.getUpdates(50, 0, true);
      const myLocalStories = localStories.filter(story => story.userId === currentUser.id && story.isStory);
      const otherLocalStories = localStories.filter(story => story.userId !== currentUser.id && story.isStory);

      if (localStories.length > 0) {
        setStories(otherLocalStories as Story[]);
        setMyStories(myLocalStories as Story[]);
      }

      // Then try to sync with Firebase
      const result = await comprehensiveUpdatesService.getStoriesFeed(currentUser.id, true);

      if (result.success && result.data) {
        setStories(result.data.stories);
        setMyStories(result.data.myStories);
      }

      // Setup real-time listener for stories using Firebase onSnapshot
      try {
        const { onSnapshot, query, collection, where, orderBy, Timestamp } = await import('firebase/firestore');
        const { db } = await import('../../src/services/firebaseSimple');

        if (!db) {
          // Firestore not available, continue with offline mode
          return;
        }

        // Get current time for expiration check
        const now = new Date();
        const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

        const storiesQuery = query(
          collection(db, 'stories'),
          where('isVisible', '==', true),
          where('timestamp', '>', Timestamp.fromDate(twentyFourHoursAgo)),
          orderBy('timestamp', 'desc')
        );

        storiesListenerRef.current = onSnapshot(
          storiesQuery,
          async (snapshot) => {
            const realtimeStories: Story[] = [];
            const realtimeMyStories: Story[] = [];
            const currentTime = new Date();

            // Process each story document with enhanced validation
            for (const doc of snapshot.docs) {
              const data = doc.data();

              // Enhanced story object with proper data validation
              const story: Story = {
                id: doc.id,
                ...data,
                isStory: true,
                timestamp: data.timestamp?.toDate() || currentTime,
                expiresAt: data.expiresAt?.toDate() || new Date(currentTime.getTime() + 24 * 60 * 60 * 1000),
                isLikedByCurrentUser: data.likes?.includes(currentUser.id) || false,
                isViewedByCurrentUser: data.views?.some((v: any) => v.userId === currentUser.id) || false,
                // Ensure all required fields are present
                likeCount: data.likeCount || 0,
                commentCount: data.commentCount || 0,
                shareCount: data.shareCount || 0,
                viewCount: data.viewCount || 0,
                media: data.media || [],
                hashtags: data.hashtags || [],
                mentions: data.mentions || [],
                groupTags: data.groupTags || [],
              } as Story;

              // Check if story is still valid (not expired) and has required data
              if (story.expiresAt > currentTime && story.userId && story.userName && story.type) {
                if (story.userId === currentUser.id) {
                  realtimeMyStories.push(story);
                } else {
                  realtimeStories.push(story);
                }
              }
            }

            // Save stories to local storage for offline access with conflict resolution
            for (const story of [...realtimeStories, ...realtimeMyStories]) {
              try {
                // Check if local version exists and compare timestamps
                const localStory = await localUpdatesStorage.getUpdateById(story.id);
                if (!localStory || new Date(localStory.timestamp).getTime() < story.timestamp.getTime()) {
                  await localUpdatesStorage.saveUpdate(story);
                }
              } catch (storageError) {
                // Continue if storage fails but log for debugging
              }
            }

            // Sort stories by timestamp for consistent ordering
            realtimeStories.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
            realtimeMyStories.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

            setStories(realtimeStories);
            setMyStories(realtimeMyStories);
            setLastSyncTime(new Date());
          },
          async (error) => {
            // Use error for debugging and fallback to local storage
            if (error.code === 'permission-denied') {
              Alert.alert('Permission Error', 'Unable to access real-time stories');
            }
            const fallbackStories = await localUpdatesStorage.getUpdates(50, 0, true);
            const myFallbackStories = fallbackStories.filter(story => story.userId === currentUser.id && story.isStory);
            const otherFallbackStories = fallbackStories.filter(story => story.userId !== currentUser.id && story.isStory);
            setStories(otherFallbackStories as Story[]);
            setMyStories(myFallbackStories as Story[]);
          }
        );
      } catch (firebaseError) {
        // Firebase not available, continue with offline mode
        Alert.alert('Offline Mode', 'Real-time stories unavailable. Using offline data.');
      }

    } catch (error) {
      Alert.alert('Error', 'Failed to setup stories listener.');
    }
  }, [currentUser?.id]);

  useEffect(() => {
    if (currentUser?.id) {
      initializeScreen();
    }

    // Cleanup listeners on unmount
    return () => {
      if (updatesListenerRef.current) {
        updatesListenerRef.current();
      }
      if (storiesListenerRef.current) {
        storiesListenerRef.current();
      }
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, [currentUser?.id, initializeScreen]);

  useEffect(() => {
    // Setup scroll listener for header animation
    const listener = scrollY.addListener(({ value }) => {
      const opacity = Math.max(0, Math.min(1, 1 - value / 100));
      headerOpacity.setValue(opacity);
    });

    return () => scrollY.removeListener(listener);
  }, [headerOpacity, scrollY]);

  const loadUpdatesFeed = useCallback(async (refresh: boolean = false) => {
    if (!currentUser?.id) return;

    try {
      // Load from local storage first (offline-first approach)
      const localUpdates = await localUpdatesStorage.getUpdates(20, refresh ? 0 : updates.length, feedConfig.includeStories);

      if (localUpdates.length > 0 && refresh) {
        setUpdates(localUpdates);
        setHasMore(localUpdates.length >= 20);
      }

      // Then try to sync with Firebase
      const cursor = refresh ? undefined : nextCursor;
      const result = await comprehensiveUpdatesService.getUpdatesFeed(
        currentUser.id,
        feedConfig,
        cursor,
        20
      );

      if (result.success && result.data) {
        const newUpdates = result.data.updates;

        if (refresh) {
          setUpdates(newUpdates);
        } else {
          setUpdates(prev => [...prev, ...newUpdates]);
        }

        setHasMore(result.data.hasMore);
        setNextCursor(result.data.nextCursor);
      }
    } catch (error) {
      // Fallback to local storage on error
      const fallbackUpdates = await localUpdatesStorage.getUpdates(20, refresh ? 0 : updates.length, feedConfig.includeStories);
      if (refresh) {
        setUpdates(fallbackUpdates);
      } else {
        setUpdates(prev => [...prev, ...fallbackUpdates]);
      }
      setHasMore(fallbackUpdates.length >= 20);
    }
  }, [currentUser?.id, feedConfig, nextCursor, updates.length]);

  const loadStoriesFeed = useCallback(async () => {
    if (!currentUser?.id) {
      console.log('📖 No current user, skipping stories feed load');
      return;
    }

    console.log('📖 Loading stories feed for user:', currentUser.id);

    try {
      // Load from local storage first (offline-first approach)
      const localStories = await localUpdatesStorage.getUpdates(50, 0, true);
      const myLocalStories = localStories.filter(story => story.userId === currentUser.id && story.isStory);
      const otherLocalStories = localStories.filter(story => story.userId !== currentUser.id && story.isStory);

      if (localStories.length > 0) {
        setStories(otherLocalStories as Story[]);
        setMyStories(myLocalStories as Story[]);
        console.log('✅ Loaded stories from local storage:', localStories.length);
      }

      // Only try Firebase if we have proper auth and are online
      if (typeof window !== 'undefined' && window.firebase?.auth) {
        try {
          const result = await comprehensiveUpdatesService.getStoriesFeed(currentUser.id, true);

          if (result.success && result.data) {
            setStories(result.data.stories);
            setMyStories(result.data.myStories);
            console.log('✅ Loaded stories from Firebase');
          }
        } catch (firebaseError) {
          console.warn('⚠️ Firebase stories sync failed, using local data only:', firebaseError);
          // Don't throw, just use local data
        }
      } else {
        console.log('⚠️ Firebase auth not available, using local data only');
      }
    } catch (error) {
      console.error('❌ Error loading stories feed:', error);

      // Fallback to local storage on error
      try {
        const fallbackStories = await localUpdatesStorage.getUpdates(50, 0, true);
        const myFallbackStories = fallbackStories.filter(story => story.userId === currentUser.id && story.isStory);
        const otherFallbackStories = fallbackStories.filter(story => story.userId !== currentUser.id && story.isStory);
        setStories(otherFallbackStories as Story[]);
        setMyStories(myFallbackStories as Story[]);
        console.log('✅ Used fallback local stories');
      } catch (fallbackError) {
        console.error('❌ Even fallback failed:', fallbackError);
        // Set empty arrays to prevent infinite loops
        setStories([]);
        setMyStories([]);
      }
    }
  }, [currentUser?.id]);

  // Load live streams only
  const loadLiveStreams = useCallback(async () => {
    if (!currentUser?.id) return;

    try {
      // Load live streams from Firebase
      const result = await comprehensiveUpdatesService.getUpdatesFeed(
        currentUser.id,
        {
          algorithm: 'chronological',
          includeStories: false,
          includeFriends: true,
          includeFollowing: true,
          includePublic: true,
          contentTypes: ['live'], // Only live content
          maxAge: 24, // Live streams from last 24 hours
          minEngagement: 0,
        },
        undefined,
        20
      );

      if (result.success && result.data) {
        // Filter for currently live streams
        const liveStreams = result.data.updates.filter(update =>
          update.type === 'live' && (update as any).isLive === true
        );
        setUpdates(liveStreams);
        setHasMore(result.data.hasMore);
        setNextCursor(result.data.nextCursor);
      }
    } catch (error) {
      console.error('❌ Error loading live streams:', error);
      setUpdates([]);
    }
  }, [currentUser?.id]);

  // Load trending content with metrics
  const loadTrendingContent = useCallback(async () => {
    if (!currentUser?.id) return;

    try {
      // Load trending content from Firebase
      const result = await comprehensiveUpdatesService.getUpdatesFeed(
        currentUser.id,
        {
          algorithm: 'engagement',
          includeStories: false,
          includeFriends: false, // Include all public content for trending
          includeFollowing: false,
          includePublic: true,
          contentTypes: ['image', 'video', 'text'],
          maxAge: 168, // Last 7 days
          minEngagement: 10, // Minimum engagement for trending
        },
        undefined,
        20
      );

      if (result.success && result.data) {
        // Sort by engagement metrics for trending
        const trendingUpdates = result.data.updates.sort((a, b) => {
          const aEngagement = a.likeCount + a.commentCount + a.shareCount;
          const bEngagement = b.likeCount + b.commentCount + b.shareCount;
          return bEngagement - aEngagement;
        });

        setUpdates(trendingUpdates);
        setHasMore(result.data.hasMore);
        setNextCursor(result.data.nextCursor);
      }
    } catch (error) {
      console.error('❌ Error loading trending content:', error);
      setUpdates([]);
    }
  }, [currentUser?.id]);

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    setSyncStatus('syncing');

    try {
      // Force sync all pending updates first if online
      if (isOnline) {
        const syncResult = await safeUpdatesSyncService.forceSyncAll();
        setPendingUpdatesCount(syncResult.failed);
      }

      // Refresh current tab content
      if (activeTab === 'feed') {
        await Promise.all([
          loadUpdatesFeed(true),
          loadStoriesFeed()
        ]);
      } else if (activeTab === 'stories') {
        await loadStoriesFeed();
      } else if (activeTab === 'live') {
        await loadLiveStreams();
      } else if (activeTab === 'trending') {
        await loadTrendingContent();
      }

      setLastSyncTime(new Date());
      setSyncStatus('idle');
    } catch (error) {
      setSyncStatus('error');
      Alert.alert('Error', 'Failed to refresh content');
    } finally {
      setIsRefreshing(false);
    }
  }, [loadStoriesFeed, loadUpdatesFeed, isOnline]);

  const handleLoadMore = useCallback(async () => {
    if (!isLoading && hasMore) {
      await loadUpdatesFeed(false);
    }
  }, [isLoading, hasMore, loadUpdatesFeed]);

  // ==================== INTERACTION HANDLERS ====================

  const handleLike = useCallback(async (updateId: string) => {
    if (!currentUser) {
      Alert.alert('Authentication Required', 'Please log in to like updates');
      return;
    }

    // Find the current update
    const currentUpdate = updates.find(u => u.id === updateId);
    if (!currentUpdate) return;

    const wasLiked = currentUpdate.isLikedByCurrentUser;
    const newLikeCount = wasLiked ? Math.max(0, currentUpdate.likeCount - 1) : currentUpdate.likeCount + 1;

    // Optimistic UI update with enhanced state management
    const optimisticUpdate = {
      ...currentUpdate,
      isLikedByCurrentUser: !wasLiked,
      likeCount: newLikeCount,
      timestamp: new Date(), // Update timestamp for conflict resolution
    };

    setUpdates(prev => prev.map(update =>
      update.id === updateId ? optimisticUpdate : update
    ));

    // Save optimistic update to local storage immediately for offline support
    try {
      await localUpdatesStorage.saveUpdate(optimisticUpdate);
    } catch (localError) {
      // Continue even if local save fails
    }

    // Removed like button animation - FAB functionality moved to header avatar

    // Try to sync with server if online
    if (isOnline) {
      try {
        const result = await comprehensiveUpdatesService.toggleLike(
          updateId,
          currentUser.id,
          currentUser.name || 'Unknown User',
          currentUser.avatar
        );

        if (result.success && result.data) {
          // Update with server response for consistency
          const serverUpdate = {
            ...optimisticUpdate,
            isLikedByCurrentUser: result.data.isLiked,
            likeCount: result.data.likeCount,
            timestamp: new Date(),
          };

          setUpdates(prev => prev.map(update =>
            update.id === updateId ? serverUpdate : update
          ));

          // Save server response to local storage
          await localUpdatesStorage.saveUpdate(serverUpdate);
        } else {
          throw new Error(result.error || 'Failed to toggle like');
        }
      } catch (error) {
        // If online sync fails, keep optimistic update and it will retry on next sync
        Alert.alert('Sync Later', 'Like saved locally and will sync when connection improves.');
      }
    } else {
      // Offline mode: save locally and will sync when online
      Alert.alert('Offline Mode', 'Like saved locally and will sync when you come back online.');
    }
  }, [currentUser, updates, isOnline]);

  const handleShare = useCallback(async (updateId: string) => {
    if (!currentUser) {
      Alert.alert('Authentication Required', 'Please log in to share updates');
      return;
    }

    try {
      const result = await comprehensiveUpdatesService.shareUpdate(
        updateId,
        currentUser.id,
        currentUser.name || 'Unknown User',
        currentUser.avatar
      );

      if (result.success && result.data) {
        // Update local state
        setUpdates(prev => prev.map(update =>
          update.id === updateId
            ? { ...update, shareCount: result.data!.shareCount }
            : update
        ));

        Alert.alert('Shared!', 'Update shared successfully');
      } else {
        throw new Error(result.error || 'Failed to share update');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to share update. Please try again.');
    }
  }, [currentUser]);

  const handleComment = useCallback((updateId: string) => {
    if (!currentUser) {
      Alert.alert('Authentication Required', 'Please log in to comment');
      return;
    }

    setSelectedUpdateId(updateId);
    setInteractionType('comments');
    setShowInteractionModal(true);
  }, [currentUser]);

  const handleViewUpdate = useCallback(async (updateId: string) => {
    if (!currentUser) return;

    try {
      await comprehensiveUpdatesService.viewUpdate(
        updateId,
        currentUser.id,
        currentUser.name || 'Unknown User',
        currentUser.avatar
      );

      // Update local view count optimistically
      setUpdates(prev => prev.map(update =>
        update.id === updateId
          ? {
              ...update,
              viewCount: update.viewCount + 1,
              isViewedByCurrentUser: true
            }
          : update
      ));
    } catch (error) {
      // Don't show error to user for view tracking - silent fail
    }
  }, [currentUser]);

  // ==================== CREATION HANDLERS ====================

  // Removed unused handlers - using handleStoryCreationOptions and handleUpdateCreationOptions instead

  // Removed openCamera - now using CameraCaptureModal for better user experience

  const openGallery = useCallback(async () => {
    if (!currentUser) {
      Alert.alert('Authentication Required', 'Please log in to select media');
      return;
    }

    try {
      // Request media library permissions
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Media library permission is required to select photos and videos');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images', 'videos'],
        allowsEditing: true,
        aspect: [9, 16],
        quality: 0.8,
        videoMaxDuration: 600, // 10 minutes max for videos
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        await processSelectedMedia(asset.uri, asset.type === 'video' ? 'video' : 'image');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to open gallery');
    }
  }, [currentUser]);

  const processSelectedMedia = useCallback(async (mediaUri: string, type: UpdateType) => {
    if (!currentUser) {
      Alert.alert('Authentication Required', 'Please log in to create content');
      return;
    }

    // Store media info and show caption modal for updates (stories can skip caption)
    setSelectedMediaUri(mediaUri);
    setSelectedMediaType(type);

    if (creationMode === 'update') {
      setShowCaptionModal(true);
    } else {
      // For stories, proceed directly with empty caption
      await uploadMediaWithCaption(mediaUri, type, '');
    }
  }, [currentUser, creationMode]);

  const uploadMediaWithCaption = useCallback(async (mediaUri: string, type: UpdateType, caption: string) => {
    if (!currentUser) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Create proper CreateUpdateData object
      const createData: CreateUpdateData = {
        type: type,
        caption: caption.trim() || undefined,
        mediaUri,
        privacy: 'public',
        isStory: creationMode === 'story',
        location: undefined,
        musicTrack: undefined,
        hashtags: [],
        mentions: [],
        groupTags: [],
        scheduledAt: undefined,
        expiresAt: creationMode === 'story' ? new Date(Date.now() + 24 * 60 * 60 * 1000) : undefined, // 24 hours for stories
      };

      // Use comprehensive updates service with proper CreateUpdateData
      const result = await comprehensiveUpdatesService.createUpdate(
        currentUser.id,
        currentUser.name || 'Unknown User',
        currentUser.avatar,
        createData,
        (progress) => {
          setUploadProgress(progress.percentage || 0);
        }
      );

      if (result.success) {
        const contentType = creationMode === 'story' ? 'Story' : 'Update';
        Alert.alert('Success!', `${contentType} posted successfully${!isOnline ? ' (will sync when online)' : ''}`);

        // Close all modals and reset state
        setShowCreateModal(false);
        setShowStoryCreator(false);
        setShowCameraCapture(false);
        setShowCaptionModal(false);
        setCaptionText('');
        setSelectedMediaUri('');

        // Refresh feeds to show new content
        await handleRefresh();

        // Use ResizeMode for video rendering if needed
        if (ResizeMode) {
          // Video will be rendered with proper resize mode
        }
      } else {
        throw new Error(result.error || 'Failed to create content');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to process media. Please try again.');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  }, [currentUser, creationMode, handleRefresh]);

  // Create text-only update using CreateUpdateData
  const createTextUpdate = useCallback(async (text: string, isStory: boolean = false) => {
    if (!currentUser || !text.trim()) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      const createData: CreateUpdateData = {
        type: 'text',
        caption: text.trim(),
        mediaUri: '', // No media for text updates
        privacy: 'public',
        isStory,
        location: undefined,
        musicTrack: undefined,
        hashtags: text.match(/#\w+/g) || [], // Extract hashtags from text
        mentions: text.match(/@\w+/g) || [], // Extract mentions from text
        groupTags: [],
        scheduledAt: undefined,
        expiresAt: isStory ? new Date(Date.now() + 24 * 60 * 60 * 1000) : undefined,
      };

      const result = await comprehensiveUpdatesService.createUpdate(
        currentUser.id,
        currentUser.name || 'Unknown User',
        currentUser.avatar,
        createData,
        (progress) => {
          setUploadProgress(progress.percentage || 0);
        }
      );

      if (result.success) {
        const contentType = isStory ? 'Story' : 'Update';
        Alert.alert('Success!', `${contentType} posted successfully${!isOnline ? ' (will sync when online)' : ''}`);

        // Refresh feeds to show new content
        await handleRefresh();
      } else {
        throw new Error(result.error || 'Failed to create text update');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to post text update. Please try again.');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  }, [currentUser, comprehensiveUpdatesService, handleRefresh, isOnline]);

  // ==================== STORY HANDLERS ====================

  const handleViewStory = useCallback(async (storyId: string) => {
    if (!currentUser) return;

    try {
      await comprehensiveUpdatesService.viewUpdate(
        storyId,
        currentUser.id,
        currentUser.name || 'Unknown User',
        currentUser.avatar
      );

      // Update local state to mark story as viewed
      setStories(prev => prev.map(story =>
        story.id === storyId
          ? { ...story, isViewedByCurrentUser: true }
          : story
      ));
    } catch (error) {
      // Don't show error to user for view tracking - silent fail
    }
  }, [currentUser]);

  const handleStoryCreationOptions = useCallback(() => {
    if (!currentUser) {
      Alert.alert('Authentication Required', 'Please log in to create stories');
      return;
    }

    setShowAvatarMenu(true);
  }, [currentUser]);

  // Removed handleUpdateCreationOptions - functionality moved to header avatar

  // ==================== NAVIGATION HANDLERS ====================

  const navigateToProfile = useCallback((userId?: string) => {
    if (userId && userId !== currentUser?.id) {
      // Navigate to other user's profile
      navigationService.navigate(ROUTES.PROFILE.VIEW(userId));
    } else {
      // Navigate to own profile
      navigationService.openProfile();
    }
  }, [currentUser?.id]);

  const navigateToSearch = useCallback(() => {
    navigationService.openGlobalSearch();
  }, []);

  const navigateToChats = useCallback(() => {
    navigationService.navigateToHome();
  }, []);

  const navigateToGroups = useCallback(() => {
    navigationService.navigate(ROUTES.TABS.GROUPS);
  }, []);

  const navigateToCalls = useCallback(() => {
    navigationService.navigate(ROUTES.TABS.CALLS);
  }, []);

  // Removed unused navigation functions - menu functionality removed

  // ==================== RENDER METHODS ====================

  const renderHeader = () => (
    <Animated.View style={[styles.header, { opacity: headerOpacity }]}>
      <View style={styles.headerContent}>
        {/* Left Side - App Logo/Title with Sync Status */}
        <View style={styles.headerLeft}>
          <TouchableOpacity onPress={navigateToChats} style={styles.titleContainer}>
            <Text style={styles.headerTitle}>IraChat</Text>
            {/* Sync Status Indicator */}
            <View style={styles.syncStatusContainer}>
              {syncStatus === 'syncing' && (
                <View style={styles.syncIndicator}>
                  <ActivityIndicator size="small" color={COLORS.primary} />
                  <Text style={styles.syncText}>Syncing...</Text>
                </View>
              )}
              {syncStatus === 'error' && (
                <View style={styles.syncIndicator}>
                  <Ionicons name="warning" size={12} color={COLORS.error} />
                  <Text style={[styles.syncText, { color: COLORS.error }]}>Sync Error</Text>
                </View>
              )}
              {!isOnline && (
                <View style={styles.syncIndicator}>
                  <Ionicons name="cloud-offline" size={12} color={COLORS.warning} />
                  <Text style={[styles.syncText, { color: COLORS.warning }]}>Offline</Text>
                </View>
              )}
              {pendingUpdatesCount > 0 && isOnline && syncStatus === 'idle' && (
                <View style={styles.syncIndicator}>
                  <Ionicons name="cloud-upload" size={12} color={COLORS.secondary} />
                  <Text style={[styles.syncText, { color: COLORS.secondary }]}>{pendingUpdatesCount} pending</Text>
                </View>
              )}
              {lastSyncTime && syncStatus === 'idle' && (
                <View style={styles.syncIndicator}>
                  <Ionicons name="checkmark-circle" size={12} color={COLORS.success} />
                  <Text style={[styles.syncText, { color: COLORS.success }]}>
                    Last sync: {formatSyncTime(lastSyncTime)}
                  </Text>
                </View>
              )}
            </View>
          </TouchableOpacity>
        </View>

        {/* Right Side - Navigation Icons with Better Alignment */}
        <View style={styles.headerRight}>
          <TouchableOpacity onPress={navigateToSearch} style={styles.headerIcon}>
            <Ionicons name="search" size={22} color={COLORS.text} />
          </TouchableOpacity>

          <TouchableOpacity onPress={navigateToGroups} style={styles.headerIcon}>
            <Ionicons name="people" size={22} color={COLORS.text} />
          </TouchableOpacity>

          <TouchableOpacity onPress={navigateToCalls} style={styles.headerIcon}>
            <Ionicons name="call" size={22} color={COLORS.text} />
          </TouchableOpacity>

          {/* Removed camera icon - redundant functionality */}

          <TouchableOpacity
            onPress={handleStoryCreationOptions}
            style={[styles.headerIcon, { position: 'relative' }]}
          >
            {currentUser?.avatar ? (
              <Image source={{ uri: currentUser.avatar }} style={styles.headerAvatar} />
            ) : (
              <Ionicons name="person-circle" size={22} color={COLORS.text} />
            )}
            {/* Plus icon overlay to indicate story creation function */}
            <View style={{
              position: 'absolute',
              bottom: -2,
              left: -2,
              width: 14,
              height: 14,
              borderRadius: 7,
              backgroundColor: COLORS.primary,
              justifyContent: 'center',
              alignItems: 'center',
              borderWidth: 1,
              borderColor: COLORS.background,
            }}>
              <Ionicons name="add" size={10} color="white" />
            </View>
          </TouchableOpacity>
        </View>
      </View>
    </Animated.View>
  );

  const renderTabBar = () => {
    // Animation values for ripple effect
    const rippleAnimations = useRef(
      tabs.map(() => new Animated.Value(0))
    ).current;

    const handleTabPress = (tab: typeof tabs[number], index: number) => {
      // Prevent rapid tab changes
      if (isScrolling) return;

      // Animate ripple effect
      Animated.sequence([
        Animated.timing(rippleAnimations[index], {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(rippleAnimations[index], {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();

      // Provide haptic feedback
      try {
        const { HapticFeedback } = require('expo-haptics');
        HapticFeedback.impactAsync(HapticFeedback.ImpactFeedbackStyle.Light);
      } catch (error) {
        // Haptic feedback not available
      }

      // Update tab immediately for responsive UI
      isUserSwiping.current = false; // This is a tap, not a swipe
      setActiveTab(tab);

      // Reset cooldown to allow immediate tab change
      lastTabChangeRef.current = Date.now();

      // Scroll to the selected tab
      if (flatListRef.current) {
        try {
          flatListRef.current.scrollToIndex({
            index: index,
            animated: true,
          });
        } catch (error) {
          // Handle scroll error gracefully
          console.warn('Tab scroll error:', error);
        }
      }
    };

    return (
      <View style={styles.tabBar}>
        <View style={styles.tabBarContent}>
          {tabs.map((tab, index) => (
            <TouchableOpacity
              key={tab}
              style={[styles.tabItem, activeTab === tab && styles.activeTabItem]}
              onPress={() => handleTabPress(tab, index)}
              activeOpacity={0.7}
            >
              {/* Ripple Effect */}
              <Animated.View
                style={[
                  styles.tabRipple,
                  {
                    opacity: rippleAnimations[index],
                    transform: [
                      {
                        scale: rippleAnimations[index].interpolate({
                          inputRange: [0, 1],
                          outputRange: [0.8, 1.2],
                        }),
                      },
                    ],
                  },
                ]}
              />

              {/* Tab Text with smooth color transition */}
              <Animated.Text
                style={[
                  styles.tabText,
                  activeTab === tab && styles.activeTabText,
                  {
                    color: activeTab === tab ? COLORS.primary : COLORS.textSecondary,
                  }
                ]}
              >
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </Animated.Text>
            </TouchableOpacity>
          ))}

          {/* Animated Tab Indicator */}
          <Animated.View
            style={[
              styles.tabIndicator,
              {
                transform: [{ translateX: tabIndicatorX }],
              },
            ]}
          />
        </View>
      </View>
    );
  };



  // Render Feed Tab Content (Full screen updates)
  const renderFeedContent = () => (
    <View style={styles.content}>
      {updates.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="newspaper" size={64} color={COLORS.textMuted} />
          <Text style={styles.emptyTitle}>No Updates Yet</Text>
          <Text style={styles.emptySubtitle}>
            Your feed is empty. Follow friends or create your first update to get started!
          </Text>
          <TouchableOpacity
            style={styles.startLiveButton}
            onPress={() => setShowCreateModal(true)}
          >
            <Ionicons name="add" size={20} color="#FFFFFF" />
            <Text style={styles.startLiveButtonText}>Create Update</Text>
          </TouchableOpacity>
        </View>
      ) : (
        /* Updates Feed - Full Screen */
        <FlatList
          data={updates}
          keyExtractor={(item, index) => `update_${item.id}_${index}_${item.timestamp?.getTime() || Date.now()}`}
          renderItem={({ item, index: _index }) => (
            <UpdateCard
              update={item}
              isActive={true}
              onLike={() => handleLike(item.id)}
              onComment={() => handleComment(item.id)}
              onShare={() => handleShare(item.id)}
              onView={() => handleViewUpdate(item.id)}
              onUserPress={(userId: string) => navigateToProfile(userId)}
              currentUser={currentUser}
              setUpdates={setUpdates}
            />
          )}
          style={styles.updatesList}
          showsVerticalScrollIndicator={false}
          pagingEnabled={true}
          snapToInterval={SCREEN_HEIGHT}
          snapToAlignment="start"
          decelerationRate="fast"
          onScroll={Animated.event(
            [{ nativeEvent: { contentOffset: { y: scrollY } } }],
            { useNativeDriver: false }
          )}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              tintColor="#FFFFFF"
            />
          }
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          contentInsetAdjustmentBehavior="never"
          // No content padding - full screen content
        />
      )}
    </View>
  );

  // Render Stories Tab Content (Grid layout, stories only)
  const renderStoriesContent = () => (
    <View style={styles.content}>
      <ScrollView
        style={styles.storiesScrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          paddingTop: 48, // Add padding so content isn't hidden behind tab bar
        }}
      >
        {/* My Stories Section */}
        <View style={styles.myStoriesSection}>
          <Text style={styles.sectionTitle}>Your Stories</Text>
          <View style={styles.storiesGrid}>
            {/* Add Story Button */}
            <TouchableOpacity style={styles.addStoryCard} onPress={handleStoryCreationOptions}>
              <View style={styles.addStoryIcon}>
                <Ionicons name="add" size={32} color={COLORS.primary} />
              </View>
              <Text style={styles.addStoryText}>Add Story</Text>
            </TouchableOpacity>

            {/* My Stories Grid */}
            {myStories.map((story, index) => (
              <TouchableOpacity
                key={`my-${story.id}`}
                style={styles.storyGridItem}
                onPress={() => {
                  setCurrentStoryIndex(index);
                  setStoryViewMode('my');
                  setShowStoryViewer(true);
                }}
              >
                <Image
                  source={{ uri: story.media[0]?.url || story.media[0]?.thumbnailUrl }}
                  style={styles.storyGridImage}
                />
                <View style={styles.storyGridOverlay}>
                  <Text style={styles.storyGridTime}>
                    {formatTimeAgo(story.timestamp)}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Friends Stories Section */}
        {stories.length > 0 && (
          <View style={styles.friendsStoriesSection}>
            <Text style={styles.sectionTitle}>Friends' Stories</Text>
            <View style={styles.storiesGrid}>
              {stories.map((story, index) => (
                <TouchableOpacity
                  key={story.id}
                  style={styles.storyGridItem}
                  onPress={() => {
                    setCurrentStoryIndex(index);
                    setStoryViewMode('friends');
                    setShowStoryViewer(true);
                    handleViewStory(story.id);
                  }}
                >
                  <Image
                    source={{ uri: story.media[0]?.url || story.media[0]?.thumbnailUrl }}
                    style={styles.storyGridImage}
                  />
                  <View style={styles.storyGridOverlay}>
                    <Text style={styles.storyGridUser}>{story.userName}</Text>
                    <Text style={styles.storyGridTime}>
                      {formatTimeAgo(story.timestamp)}
                    </Text>
                  </View>
                  {!story.isViewedByCurrentUser && <View style={styles.unviewedIndicator} />}
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}

        {/* Empty State */}
        {stories.length === 0 && myStories.length === 0 && (
          <View style={styles.emptyStoriesContainer}>
            <Ionicons name="camera" size={64} color={COLORS.textMuted} />
            <Text style={styles.emptyStoriesText}>No Stories Yet</Text>
            <Text style={styles.emptyStoriesSubtext}>
              Share your moments with friends by creating your first story
            </Text>
            <TouchableOpacity style={styles.createStoryButton} onPress={handleStoryCreationOptions}>
              <Text style={styles.createStoryButtonText}>Create Story</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </View>
  );

  // Render Live Tab Content (Live streams with special UI)
  const renderLiveContent = () => (
    <View style={styles.content}>
      {updates.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="radio" size={64} color={COLORS.textMuted} />
          <Text style={styles.emptyTitle}>No Live Streams</Text>
          <Text style={styles.emptySubtitle}>
            No one is currently live. Check back later or start your own live stream!
          </Text>
          <TouchableOpacity style={styles.startLiveButton} onPress={() => setShowCreateModal(true)}>
            <Ionicons name="radio" size={20} color="#FFFFFF" />
            <Text style={styles.startLiveButtonText}>Go Live</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={updates}
          keyExtractor={(item, index) => `live_${item.id}_${index}_${item.timestamp?.getTime() || Date.now()}`}
          renderItem={({ item }) => (
            <View style={styles.liveStreamCard}>
              <View style={styles.liveStreamHeader}>
                <View style={styles.liveIndicator}>
                  <View style={styles.liveDot} />
                  <Text style={styles.liveText}>LIVE</Text>
                </View>
                <Text style={styles.viewerCount}>
                  {(item as any).viewerCount || 0} viewers
                </Text>
              </View>
              <UpdateCard
                update={item}
                isActive={true}
                onLike={() => handleLike(item.id)}
                onComment={() => handleComment(item.id)}
                onShare={() => handleShare(item.id)}
                onView={() => handleViewUpdate(item.id)}
                onUserPress={(userId: string) => navigateToProfile(userId)}
                currentUser={currentUser}
                setUpdates={setUpdates}
              />
            </View>
          )}
          style={styles.liveStreamsList}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{
            paddingTop: 48, // Add padding so first item isn't hidden behind tab bar
          }}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              tintColor="#FFFFFF"
            />
          }
        />
      )}
    </View>
  );

  // Render Trending Tab Content (Trending posts with metrics)
  const renderTrendingContent = () => (
    <View style={styles.content}>
      {updates.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="trending-up" size={64} color={COLORS.textMuted} />
          <Text style={styles.emptyTitle}>No Trending Content</Text>
          <Text style={styles.emptySubtitle}>
            Check back later for trending posts and viral content
          </Text>
        </View>
      ) : (
        <FlatList
          data={updates}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <View style={styles.trendingCard}>
              <View style={styles.trendingHeader}>
                <View style={styles.trendingBadge}>
                  <Ionicons name="trending-up" size={16} color="#FF6B6B" />
                  <Text style={styles.trendingText}>Trending</Text>
                </View>
                <View style={styles.engagementMetrics}>
                  <Text style={styles.engagementText}>
                    {item.likeCount + item.commentCount + item.shareCount} interactions
                  </Text>
                </View>
              </View>
              <UpdateCard
                update={item}
                isActive={true}
                onLike={() => handleLike(item.id)}
                onComment={() => handleComment(item.id)}
                onShare={() => handleShare(item.id)}
                onView={() => handleViewUpdate(item.id)}
                onUserPress={(userId: string) => navigateToProfile(userId)}
                currentUser={currentUser}
                setUpdates={setUpdates}
              />
            </View>
          )}
          style={styles.trendingList}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{
            paddingTop: 48, // Add padding so first item isn't hidden behind tab bar
          }}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              tintColor="#FFFFFF"
            />
          }
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
        />
      )}
    </View>
  );

  // Removed unused renderTabContent function - now using horizontal FlatList

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000" />

      {/* Horizontal Swipeable Tab Content - Like Updates Page */}
      {isLoading && updates.length === 0 ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>Loading updates...</Text>
        </View>
      ) : (
        <FlatList
          ref={flatListRef}
          data={tabs}
          keyExtractor={(item) => item}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onScroll={handleScroll}
          onScrollBeginDrag={handleScrollBeginDrag}
          onMomentumScrollEnd={handleMomentumScrollEnd}
          scrollEventThrottle={16}
          snapToInterval={SCREEN_WIDTH}
          snapToAlignment="start"
          decelerationRate="fast"
          bounces={false}
          renderItem={({ item: tab }) => (
            <View style={{ width: SCREEN_WIDTH, flex: 1 }}>
              {tab === 'feed' && renderFeedContent()}
              {tab === 'stories' && renderStoriesContent()}
              {tab === 'live' && renderLiveContent()}
              {tab === 'trending' && renderTrendingContent()}
            </View>
          )}
          getItemLayout={(_data, index) => ({
            length: SCREEN_WIDTH,
            offset: SCREEN_WIDTH * index,
            index,
          })}
          initialScrollIndex={tabs.indexOf(activeTab)}
          onScrollToIndexFailed={(info) => {
            // Handle scroll to index failure
            setTimeout(() => {
              if (flatListRef.current) {
                flatListRef.current.scrollToIndex({
                  index: info.index,
                  animated: false,
                });
              }
            }, 100);
          }}
        />
      )}

      {/* Floating Header Overlay */}
      {renderHeader()}

      {/* Floating Tab Bar Overlay */}
      {renderTabBar()}

      {/* Removed floating stories overlay - redundant with header avatar */}
      {/* Removed FAB - functionality moved to header avatar */}

      {/* Story Viewer Modal */}
      {showStoryViewer && (
        <StoryViewerModal
          visible={showStoryViewer}
          stories={storyViewMode === 'my' ? myStories : stories}
          currentIndex={currentStoryIndex}
          onClose={() => setShowStoryViewer(false)}
          onStoryChange={setCurrentStoryIndex}
          currentUserId={currentUser?.id}
        />
      )}

      {/* Create Update Modal */}
      {showCreateModal && (
        <CreateUpdateModal
          visible={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onCameraPress={() => {
            setCreationMode('update');
            setShowCameraCapture(true);
          }}
          currentUser={currentUser}
        />
      )}

      {/* Story Creator Modal */}
      {showStoryCreator && (
        <StoryCreatorModal
          visible={showStoryCreator}
          onClose={() => setShowStoryCreator(false)}
          onCameraPress={() => {
            setCreationMode('story');
            setShowCameraCapture(true);
          }}
          currentUser={currentUser}
        />
      )}

      {/* Interaction Modal (Comments, Likes, etc.) */}
      {showInteractionModal && (
        <InteractionModal
          visible={showInteractionModal}
          updateId={selectedUpdateId}
          type={interactionType}
          onClose={() => setShowInteractionModal(false)}
          currentUser={currentUser}
        />
      )}

      {/* Camera Capture Modal */}
      {showCameraCapture && (
        <CameraCaptureModal
          visible={showCameraCapture}
          onClose={() => setShowCameraCapture(false)}
          onMediaCapture={processSelectedMedia}
          isStory={creationMode === 'story'}
        />
      )}

      {/* Modern Avatar Menu Modal */}
      {showAvatarMenu && (
        <AvatarMenuModal
          visible={showAvatarMenu}
          onClose={() => setShowAvatarMenu(false)}
          onTextStory={() => {
            setShowAvatarMenu(false);
            setShowTextStoryModal(true);
          }}
          onCamera={() => {
            setShowAvatarMenu(false);
            setCreationMode('story');
            setShowCameraCapture(true);
          }}
          onGallery={() => {
            setShowAvatarMenu(false);
            setCreationMode('story');
            openGallery();
          }}
          currentUser={currentUser}
        />
      )}

      {/* Modern Text Story Modal */}
      {showTextStoryModal && (
        <TextStoryModal
          visible={showTextStoryModal}
          onClose={() => setShowTextStoryModal(false)}
          onSubmit={(text) => {
            setShowTextStoryModal(false);
            createTextUpdate(text, true);
          }}
          currentUser={currentUser}
        />
      )}

      {/* Caption Input Modal */}
      {showCaptionModal && (
        <CaptionInputModal
          visible={showCaptionModal}
          mediaUri={selectedMediaUri}
          mediaType={selectedMediaType}
          caption={captionText}
          onCaptionChange={setCaptionText}
          onPost={() => {
            uploadMediaWithCaption(selectedMediaUri, selectedMediaType, captionText);
          }}
          onClose={() => {
            setShowCaptionModal(false);
            setCaptionText('');
            setSelectedMediaUri('');
          }}
          isUploading={isUploading}
          uploadProgress={uploadProgress}
        />
      )}
    </SafeAreaView>
  );
}

// Real UpdateCard component with full functionality
const UpdateCard = ({
  update,
  isActive: _isActive,
  onLike,
  onComment,
  onShare,
  onView: _onView,
  currentUser,
  setUpdates,
  onUserPress
}: any) => {
  const [commentText, setCommentText] = useState('');
  const [showCommentInput, setShowCommentInput] = useState(false);

  const handleCommentPress = () => {
    setShowCommentInput(!showCommentInput);
    onComment();
  };

  const renderMedia = () => {
    if (update.type === 'video' && update.mediaUri) {
      return (
        <Video
          source={{ uri: update.mediaUri }}
          style={updateCardStyles.mediaContent}
          resizeMode={ResizeMode.COVER}
          shouldPlay={false}
          isLooping={true}
          useNativeControls={true}
        />
      );
    } else if (update.type === 'image' && update.mediaUri) {
      return (
        <Image
          source={{ uri: update.mediaUri }}
          style={updateCardStyles.mediaContent}
          resizeMode="cover"
        />
      );
    }
    return null;
  };

  return (
    <View style={updateCardStyles.updateCard}>
      {/* User info header with enhanced alignment */}
      <TouchableOpacity
        style={updateCardStyles.userHeader}
        onPress={() => onUserPress && onUserPress(update.userId)}
      >
        <View style={updateCardStyles.avatarContainer}>
          {update.userAvatar ? (
            <Image source={{ uri: update.userAvatar }} style={updateCardStyles.userAvatar} />
          ) : (
            <View style={[updateCardStyles.userAvatar, updateCardStyles.defaultAvatar]}>
              <Ionicons name="person" size={20} color={COLORS.textMuted} />
            </View>
          )}
        </View>
        <View style={updateCardStyles.userInfo}>
          <View style={updateCardStyles.userNameRow}>
            <Text style={updateCardStyles.userName}>{update.userName || 'Unknown User'}</Text>
            {update.isStory && (
              <View style={updateCardStyles.storyBadge}>
                <Text style={updateCardStyles.storyBadgeText}>Story</Text>
              </View>
            )}
          </View>
          <View style={updateCardStyles.timestampRow}>
            <Text style={updateCardStyles.timestamp}>
              {update.timestamp.toLocaleDateString()} • {update.timestamp.toLocaleTimeString()}
            </Text>
            {/* Sync status indicator */}
            {update.isPendingSync && (
              <View style={updateCardStyles.syncIndicator}>
                <Ionicons name="cloud-upload-outline" size={12} color={COLORS.warning} />
                <Text style={updateCardStyles.syncText}>Syncing...</Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>

      {/* Media content */}
      {renderMedia()}

      {/* Caption */}
      <Text style={updateCardStyles.updateText}>{update.caption || 'No caption'}</Text>

      {showCommentInput && (
        <View style={updateCardStyles.commentInputContainer}>
          <TextInput
            style={updateCardStyles.commentInput}
            placeholder="Add a comment..."
            placeholderTextColor={COLORS.textMuted}
            value={commentText}
            onChangeText={setCommentText}
            multiline
          />
          <TouchableOpacity
            style={updateCardStyles.sendButton}
            onPress={async () => {
              if (commentText.trim() && currentUser) {
                try {
                  const result = await comprehensiveUpdatesService.addComment(
                    update.id,
                    currentUser.id,
                    currentUser.name || 'Unknown User',
                    currentUser.avatar,
                    commentText.trim()
                  );

                  if (result.success) {
                    setCommentText('');
                    setShowCommentInput(false);

                    // Update local comment count optimistically
                    setUpdates((prev: Update[]) => prev.map((u: Update) =>
                      u.id === update.id
                        ? { ...u, commentCount: u.commentCount + 1 }
                        : u
                    ));
                  } else {
                    Alert.alert('Error', 'Failed to submit comment. Please try again.');
                  }
                } catch (error) {
                  Alert.alert('Error', 'Failed to submit comment. Please try again.');
                }
              }
            }}
          >
            <Ionicons name="send" size={20} color={COLORS.primary} />
          </TouchableOpacity>
        </View>
      )}

      {/* Enhanced action buttons with counts and better alignment */}
      <View style={updateCardStyles.updateActions}>
        <TouchableOpacity
          style={updateCardStyles.actionButton}
          onPress={onLike}
          activeOpacity={0.7}
        >
          <Ionicons
            name={update.isLikedByCurrentUser ? "heart" : "heart-outline"}
            size={22}
            color={update.isLikedByCurrentUser ? "#ff3040" : COLORS.text}
          />
          <Text style={updateCardStyles.actionCount}>{update.likeCount || 0}</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={updateCardStyles.actionButton}
          onPress={handleCommentPress}
          activeOpacity={0.7}
        >
          <Ionicons name="chatbubble-outline" size={22} color={COLORS.text} />
          <Text style={updateCardStyles.actionCount}>{update.commentCount || 0}</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={updateCardStyles.actionButton}
          onPress={onShare}
          activeOpacity={0.7}
        >
          <Ionicons name="share-outline" size={22} color={COLORS.text} />
          <Text style={updateCardStyles.actionCount}>{update.shareCount || 0}</Text>
        </TouchableOpacity>

        {/* View count indicator */}
        <View style={updateCardStyles.viewCount}>
          <Ionicons name="eye-outline" size={16} color={COLORS.textMuted} />
          <Text style={updateCardStyles.viewCountText}>{update.viewCount || 0} views</Text>
        </View>
      </View>
    </View>
  );
};

// Old styles object removed - now using responsive styles inside component

// UpdateCard styles (static since it's outside component)
const updateCardStyles = StyleSheet.create({
  updateCard: {
    backgroundColor: COLORS.surface,
    margin: 16,
    borderRadius: 12,
    padding: 16,
  },
  userHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  defaultAvatar: {
    backgroundColor: COLORS.surfaceLight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  userName: {
    color: COLORS.text,
    fontSize: 16,
    fontWeight: '600',
  },
  mediaContent: {
    width: '100%',
    height: 300,
    borderRadius: 8,
    marginBottom: 12,
    backgroundColor: COLORS.surfaceLight,
  },
  updateText: {
    color: COLORS.text,
    fontSize: 16,
    marginBottom: 12,
  },
  commentInputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 12,
    paddingHorizontal: 8,
    paddingVertical: 8,
    backgroundColor: COLORS.surfaceLight,
    borderRadius: 8,
  },
  commentInput: {
    flex: 1,
    color: COLORS.text,
    fontSize: 14,
    maxHeight: 80,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
  },
  sendButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: COLORS.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  updateActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: COLORS.surfaceLight,
  },
  avatarContainer: {
    marginRight: 12,
  },
  userInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  userNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  timestampRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  timestamp: {
    color: COLORS.textMuted,
    fontSize: 12,
  },
  storyBadge: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    marginLeft: 8,
  },
  storyBadgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
  },
  syncIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  syncText: {
    color: COLORS.warning,
    fontSize: 10,
    fontWeight: '500',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: COLORS.surfaceLight,
    gap: 6,
  },
  actionCount: {
    color: COLORS.text,
    fontSize: 12,
    fontWeight: '600',
  },
  viewCount: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 'auto',
    gap: 4,
  },
  viewCountText: {
    color: COLORS.textMuted,
    fontSize: 11,
  },
});

// ==================== MODAL COMPONENTS ====================

// Simple Story Viewer Modal Component
const StoryViewerModal = ({ visible, stories, currentIndex, onClose, onStoryChange, currentUserId }: any) => {
  // Handle Android back button
  React.useEffect(() => {
    if (!visible) return;

    const handleBackPress = () => {
      onClose();
      return true; // Prevent default back action
    };

    // Add back button listener for Android
    const backHandler = require('react-native').BackHandler;
    const subscription = backHandler?.addEventListener('hardwareBackPress', handleBackPress);

    return () => {
      subscription?.remove();
    };
  }, [visible, onClose]);

  if (!visible || !stories || stories.length === 0) return null;

  const currentStory = stories[currentIndex];
  if (!currentStory) return null;

  // Use onStoryChange for navigation between stories
  const handleNextStory = () => {
    if (currentIndex < stories.length - 1) {
      onStoryChange(currentIndex + 1);
    }
  };

  const handlePrevStory = () => {
    if (currentIndex > 0) {
      onStoryChange(currentIndex - 1);
    }
  };

  // Use currentUserId to check if this is user's own story
  const isOwnStory = currentStory.userId === currentUserId;

  return (
    <View style={{
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'black',
      zIndex: 1000,
      justifyContent: 'center',
      alignItems: 'center',
    }}>
      {/* Swipe down to dismiss gesture area */}
      <TouchableOpacity
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: 100,
          zIndex: 999,
        }}
        onPress={onClose}
        activeOpacity={1}
      />
      {/* Back Button */}
      <TouchableOpacity
        style={{
          position: 'absolute',
          top: 50,
          left: 20,
          zIndex: 1001,
          backgroundColor: 'rgba(0,0,0,0.5)',
          borderRadius: 20,
          padding: 8,
        }}
        onPress={onClose}
      >
        <Ionicons name="arrow-back" size={24} color="white" />
      </TouchableOpacity>

      {/* Close Button */}
      <TouchableOpacity
        style={{
          position: 'absolute',
          top: 50,
          right: 20,
          zIndex: 1001,
          backgroundColor: 'rgba(0,0,0,0.5)',
          borderRadius: 20,
          padding: 8,
        }}
        onPress={onClose}
      >
        <Ionicons name="close" size={24} color="white" />
      </TouchableOpacity>

      {currentStory.type === 'video' && currentStory.media[0]?.url ? (
        <Video
          source={{ uri: currentStory.media[0].url }}
          style={{ width: '100%', height: '100%' }}
          resizeMode={ResizeMode.CONTAIN}
          shouldPlay={true}
          isLooping={false}
          useNativeControls={false}
        />
      ) : currentStory.media[0]?.url ? (
        <Image
          source={{ uri: currentStory.media[0].url }}
          style={{ width: '100%', height: '100%' }}
          resizeMode="contain"
        />
      ) : (
        <Text style={{ color: 'white', fontSize: 18 }}>Story content unavailable</Text>
      )}

      {/* Navigation buttons */}
      <TouchableOpacity
        style={{ position: 'absolute', left: 20, top: '50%', zIndex: 1001 }}
        onPress={handlePrevStory}
        disabled={currentIndex === 0}
      >
        <Ionicons name="chevron-back" size={30} color={currentIndex === 0 ? 'gray' : 'white'} />
      </TouchableOpacity>

      <TouchableOpacity
        style={{ position: 'absolute', right: 20, top: '50%', zIndex: 1001 }}
        onPress={handleNextStory}
        disabled={currentIndex === stories.length - 1}
      >
        <Ionicons name="chevron-forward" size={30} color={currentIndex === stories.length - 1 ? 'gray' : 'white'} />
      </TouchableOpacity>

      <View style={{
        position: 'absolute',
        bottom: 100,
        left: 20,
        right: 20,
      }}>
        <Text style={{ color: 'white', fontSize: 16, fontWeight: 'bold' }}>
          {currentStory.userName} {isOwnStory ? '(You)' : ''}
        </Text>
        {currentStory.caption && (
          <Text style={{ color: 'white', fontSize: 14, marginTop: 8 }}>
            {currentStory.caption}
          </Text>
        )}

        {/* Navigation hint */}
        <Text style={{
          color: 'rgba(255,255,255,0.7)',
          fontSize: 12,
          marginTop: 12,
          textAlign: 'center'
        }}>
          Tap left/right to navigate • Tap back button to close
        </Text>
      </View>
    </View>
  );
};

// Real Create Update Modal Component
const CreateUpdateModal = ({ visible, onClose, onCameraPress, currentUser }: any) => {
  if (!visible) return null;

  return (
    <View style={{
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0,0,0,0.8)',
      zIndex: 1000,
      justifyContent: 'center',
      alignItems: 'center',
    }}>
      <View style={{
        backgroundColor: COLORS.surface,
        borderRadius: 12,
        padding: 20,
        width: '90%',
        maxWidth: 400,
      }}>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 20 }}>
          <Text style={{ color: COLORS.text, fontSize: 18, fontWeight: 'bold' }}>
            Create Update
          </Text>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color={COLORS.text} />
          </TouchableOpacity>
        </View>

        {/* User info */}
        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 15 }}>
          {currentUser?.avatar ? (
            <Image source={{ uri: currentUser.avatar }} style={{ width: 32, height: 32, borderRadius: 16 }} />
          ) : (
            <Ionicons name="person-circle" size={32} color={COLORS.textSecondary} />
          )}
          <Text style={{ color: COLORS.text, marginLeft: 10, fontWeight: '500' }}>
            {currentUser?.name || 'User'}
          </Text>
        </View>

        <Text style={{ color: COLORS.textSecondary, marginBottom: 20, textAlign: 'center' }}>
          Choose how you want to create your update
        </Text>

        {/* Camera Button */}
        <TouchableOpacity
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: COLORS.surfaceLight,
            padding: 15,
            borderRadius: 8,
            marginBottom: 10,
          }}
          onPress={() => {
            onClose();
            onCameraPress();
          }}
        >
          <Ionicons name="camera" size={24} color={COLORS.primary} />
          <Text style={{ color: COLORS.text, fontSize: 16, marginLeft: 15, fontWeight: '500' }}>
            Take Photo/Video
          </Text>
        </TouchableOpacity>

        {/* Removed Gallery Button - already available in avatar menu */}

        {/* Removed Cancel Button - X button already available in header */}
      </View>
    </View>
  );
};

// Real Story Creator Modal Component
const StoryCreatorModal = ({ visible, onClose, onCameraPress, currentUser }: any) => {
  if (!visible) return null;

  return (
    <View style={{
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0,0,0,0.8)',
      zIndex: 1000,
      justifyContent: 'center',
      alignItems: 'center',
    }}>
      <View style={{
        backgroundColor: COLORS.surface,
        borderRadius: 12,
        padding: 20,
        width: '90%',
        maxWidth: 400,
      }}>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 20 }}>
          <Text style={{ color: COLORS.text, fontSize: 18, fontWeight: 'bold' }}>
            Create Story
          </Text>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color={COLORS.text} />
          </TouchableOpacity>
        </View>

        {/* User info */}
        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 15 }}>
          {currentUser?.avatar ? (
            <Image source={{ uri: currentUser.avatar }} style={{ width: 32, height: 32, borderRadius: 16 }} />
          ) : (
            <Ionicons name="person-circle" size={32} color={COLORS.textSecondary} />
          )}
          <Text style={{ color: COLORS.text, marginLeft: 10, fontWeight: '500' }}>
            {currentUser?.name || 'User'}
          </Text>
        </View>

        <Text style={{ color: COLORS.textSecondary, marginBottom: 20, textAlign: 'center' }}>
          Share a moment with your story (expires in 24 hours)
        </Text>

        {/* Camera Button */}
        <TouchableOpacity
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: COLORS.surfaceLight,
            padding: 15,
            borderRadius: 8,
            marginBottom: 10,
          }}
          onPress={() => {
            onClose();
            onCameraPress();
          }}
        >
          <Ionicons name="camera" size={24} color={COLORS.primary} />
          <Text style={{ color: COLORS.text, fontSize: 16, marginLeft: 15, fontWeight: '500' }}>
            Take Photo/Video
          </Text>
        </TouchableOpacity>

        {/* Removed Gallery Button - already available in avatar menu */}

        {/* Removed Cancel Button - X button already available in header */}
      </View>
    </View>
  );
};

// Real Interaction Modal Component with Firebase data
const InteractionModal = ({ visible, updateId, type, onClose, currentUser }: any) => {
  const [data, setData] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [newComment, setNewComment] = React.useState('');

  React.useEffect(() => {
    if (visible && updateId) {
      loadInteractionData();
    }
  }, [visible, updateId, type]);

  const loadInteractionData = async () => {
    if (!updateId) return;

    setLoading(true);
    try {
      if (type === 'comments') {
        const result = await comprehensiveUpdatesService.getComments(updateId);
        if (result.success && result.comments) {
          setData(result.comments);
        }
      } else if (type === 'likes') {
        const result = await comprehensiveUpdatesService.getLikes(updateId);
        if (result.success && result.likes) {
          setData(result.likes);
        }
      } else if (type === 'shares') {
        // For now, shares data is embedded in the update document
        // TODO: Implement dedicated getShares method in service
        setData([]);
      }
    } catch (error) {
      // Silent fail for interaction data loading
    } finally {
      setLoading(false);
    }
  };

  const handleAddComment = async () => {
    if (!newComment.trim() || !currentUser) return;

    try {
      const result = await comprehensiveUpdatesService.addComment(
        updateId,
        currentUser.id,
        currentUser.name || 'Unknown User',
        currentUser.avatar,
        newComment.trim()
      );

      if (result.success) {
        setNewComment('');
        await loadInteractionData(); // Reload comments
      }
    } catch (error) {
      // Silent fail for adding comment
    }
  };

  if (!visible) return null;

  return (
    <View style={{
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0,0,0,0.8)',
      zIndex: 1000,
      justifyContent: 'center',
      alignItems: 'center',
    }}>
      <View style={{
        backgroundColor: COLORS.surface,
        borderRadius: 12,
        padding: 20,
        width: '90%',
        maxWidth: 400,
        maxHeight: '80%',
      }}>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 20 }}>
          <Text style={{ color: COLORS.text, fontSize: 18, fontWeight: 'bold' }}>
            {type.charAt(0).toUpperCase() + type.slice(1)}
          </Text>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color={COLORS.text} />
          </TouchableOpacity>
        </View>

        {loading ? (
          <View style={{ padding: 40, alignItems: 'center' }}>
            <ActivityIndicator size="large" color={COLORS.primary} />
            <Text style={{ color: COLORS.textSecondary, marginTop: 10 }}>Loading...</Text>
          </View>
        ) : (
          <ScrollView style={{ maxHeight: 300, marginBottom: 20 }}>
            {type === 'comments' ? (
              data.length > 0 ? (
                data.map((comment: any, index: number) => (
                  <View key={index} style={{
                    flexDirection: 'row',
                    marginBottom: 15,
                    padding: 10,
                    backgroundColor: COLORS.surfaceLight,
                    borderRadius: 8,
                  }}>
                    {comment.userAvatar ? (
                      <Image source={{ uri: comment.userAvatar }} style={{
                        width: 32,
                        height: 32,
                        borderRadius: 16,
                        marginRight: 10,
                      }} />
                    ) : (
                      <View style={{
                        width: 32,
                        height: 32,
                        borderRadius: 16,
                        backgroundColor: COLORS.surface,
                        justifyContent: 'center',
                        alignItems: 'center',
                        marginRight: 10,
                      }}>
                        <Ionicons name="person" size={16} color={COLORS.textMuted} />
                      </View>
                    )}
                    <View style={{ flex: 1 }}>
                      <Text style={{ color: COLORS.text, fontWeight: 'bold', fontSize: 14 }}>
                        {comment.userName}
                      </Text>
                      <Text style={{ color: COLORS.textSecondary, fontSize: 14, marginTop: 2 }}>
                        {comment.text}
                      </Text>
                    </View>
                  </View>
                ))
              ) : (
                <Text style={{ color: COLORS.textSecondary, textAlign: 'center', padding: 20 }}>
                  No comments yet
                </Text>
              )
            ) : type === 'likes' ? (
              data.length > 0 ? (
                data.map((like: any, index: number) => (
                  <View key={index} style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginBottom: 15,
                    padding: 10,
                    backgroundColor: COLORS.surfaceLight,
                    borderRadius: 8,
                  }}>
                    <View style={{
                      width: 32,
                      height: 32,
                      borderRadius: 16,
                      backgroundColor: COLORS.surface,
                      justifyContent: 'center',
                      alignItems: 'center',
                      marginRight: 10,
                    }}>
                      <Ionicons name="person" size={16} color={COLORS.textMuted} />
                    </View>
                    <View style={{ flex: 1 }}>
                      <Text style={{ color: COLORS.text, fontWeight: 'bold', fontSize: 14 }}>
                        User {like.userId}
                      </Text>
                      <Text style={{ color: COLORS.textSecondary, fontSize: 12 }}>
                        Liked this update
                      </Text>
                    </View>
                    <Ionicons name="heart" size={16} color="#EF4444" />
                  </View>
                ))
              ) : (
                <Text style={{ color: COLORS.textSecondary, textAlign: 'center', padding: 20 }}>
                  No likes yet
                </Text>
              )
            ) : (
              <Text style={{ color: COLORS.textSecondary, textAlign: 'center', padding: 20 }}>
                {type === 'shares' ? 'No shares yet' : `${type} data will be shown here`}
              </Text>
            )}
          </ScrollView>
        )}

        {type === 'comments' && currentUser && (
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: COLORS.surfaceLight,
            borderRadius: 8,
            padding: 10,
          }}>
            <TextInput
              style={{
                flex: 1,
                color: COLORS.text,
                fontSize: 14,
                paddingHorizontal: 10,
              }}
              placeholder="Add a comment..."
              placeholderTextColor={COLORS.textMuted}
              value={newComment}
              onChangeText={setNewComment}
              multiline
            />
            <TouchableOpacity
              onPress={handleAddComment}
              disabled={!newComment.trim()}
              style={{
                backgroundColor: newComment.trim() ? COLORS.primary : COLORS.textMuted,
                borderRadius: 16,
                padding: 8,
                marginLeft: 10,
              }}
            >
              <Ionicons name="send" size={16} color="white" />
            </TouchableOpacity>
          </View>
        )}
      </View>
    </View>
  );
};

// Real Camera Capture Modal Component - Uses expo-camera for native camera experience
const CameraCaptureModal = ({ visible, onClose, onMediaCapture, isStory }: any) => {
  const [isCapturing, setIsCapturing] = React.useState(false);
  const [hasPermission, setHasPermission] = React.useState<boolean | null>(null);
  const [cameraType, setCameraType] = React.useState<CameraType>('back');
  const [isRecording, setIsRecording] = React.useState(false);
  const [showNativeCamera, setShowNativeCamera] = React.useState(false);
  const [cameraMode, setCameraMode] = React.useState<'picture' | 'video'>('picture');
  const cameraRef = React.useRef<CameraView>(null);
  const [permission, requestPermission] = useCameraPermissions();

  // Handle Android back button
  React.useEffect(() => {
    if (!visible) return;

    const handleBackPress = () => {
      if (showNativeCamera) {
        setShowNativeCamera(false);
        if (isRecording) {
          stopRecording();
        }
        return true; // Prevent default back action
      } else {
        onClose();
        return true; // Prevent default back action
      }
    };

    // Add back button listener for Android
    const backHandler = require('react-native').BackHandler;
    const subscription = backHandler?.addEventListener('hardwareBackPress', handleBackPress);

    return () => {
      subscription?.remove();
    };
  }, [visible, showNativeCamera, isRecording, onClose]);

  React.useEffect(() => {
    if (visible) {
      if (!permission) {
        // Camera permissions are still loading
        return;
      }

      if (!permission.granted) {
        // Request permissions if not granted
        requestPermission();
      } else {
        setHasPermission(true);
      }
    } else {
      // Reset state when modal closes
      setShowNativeCamera(false);
      setIsRecording(false);
      setCameraMode('picture');
    }
  }, [visible, permission]);

  React.useEffect(() => {
    if (permission) {
      setHasPermission(permission.granted);

      if (!permission.granted && visible) {
        Alert.alert(
          'Permission Required',
          'Camera permission is required to take photos and videos',
          [
            { text: 'Cancel', onPress: onClose },
            { text: 'Grant Permission', onPress: requestPermission }
          ]
        );
      }
    }
  }, [permission, visible]);

  const takePicture = async () => {
    if (!cameraRef.current || isCapturing) return;

    try {
      setIsCapturing(true);

      const photo = await cameraRef.current.takePictureAsync({
        quality: 0.8,
        base64: false,
        skipProcessing: false,
      });

      if (photo?.uri) {
        onMediaCapture(photo.uri, 'image');
        onClose();
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to take picture. Please try again.');
    } finally {
      setIsCapturing(false);
    }
  };

  const startRecording = async () => {
    if (!cameraRef.current || isRecording) return;

    try {
      setIsRecording(true);

      const video = await cameraRef.current.recordAsync({
        maxDuration: 600, // 10 minutes max for videos
      });

      if (video?.uri) {
        onMediaCapture(video.uri, 'video');
        onClose();
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to record video. Please try again.');
    } finally {
      setIsRecording(false);
    }
  };

  const stopRecording = () => {
    if (!cameraRef.current || !isRecording) return;

    try {
      cameraRef.current.stopRecording();
      setIsRecording(false);
    } catch (error) {
      Alert.alert('Error', 'Failed to stop recording');
      setIsRecording(false);
    }
  };

  const toggleCameraType = () => {
    setCameraType(
      cameraType === 'back' ? 'front' : 'back'
    );
  };

  const openCameraForPhoto = () => {
    setCameraMode('picture');
    setShowNativeCamera(true);
  };

  const openCameraForVideo = () => {
    setCameraMode('video');
    setShowNativeCamera(true);
  };

  // Removed openGallery - gallery access is now handled directly from avatar menu

  if (!visible) return null;

  // Show native camera interface
  if (showNativeCamera && hasPermission) {
    return (
      <View style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'black',
        zIndex: 1000,
      }}>
        <CameraView
          ref={cameraRef}
          style={{ flex: 1 }}
          facing={cameraType}
          mode={cameraMode}
          videoQuality="720p"
        >
          {/* Camera Controls Overlay */}
          <View style={{
            position: 'absolute',
            top: 50,
            left: 20,
            right: 20,
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}>
            {/* Close Button */}
            <TouchableOpacity
              onPress={() => {
                setShowNativeCamera(false);
                if (isRecording) {
                  stopRecording();
                }
                // Also close the main camera modal
                onClose();
              }}
              style={{
                backgroundColor: 'rgba(0,0,0,0.7)',
                borderRadius: 20,
                padding: 12,
              }}
            >
              <Ionicons name="close" size={26} color="white" />
            </TouchableOpacity>

            {/* Camera Mode Indicator */}
            <View style={{
              backgroundColor: 'rgba(0,0,0,0.5)',
              borderRadius: 15,
              paddingHorizontal: 12,
              paddingVertical: 6,
            }}>
              <Text style={{ color: 'white', fontSize: 14, fontWeight: '500' }}>
                {cameraMode === 'picture' ? 'Photo' : 'Video'}
              </Text>
            </View>

            {/* Flip Camera Button */}
            <TouchableOpacity
              onPress={toggleCameraType}
              style={{
                backgroundColor: 'rgba(0,0,0,0.5)',
                borderRadius: 20,
                padding: 10,
              }}
            >
              <Ionicons name="camera-reverse" size={24} color="white" />
            </TouchableOpacity>
          </View>

          {/* Bottom Controls */}
          <View style={{
            position: 'absolute',
            bottom: 50,
            left: 0,
            right: 0,
            alignItems: 'center',
          }}>
            {/* Mode Switcher */}
            <View style={{
              flexDirection: 'row',
              backgroundColor: 'rgba(0,0,0,0.5)',
              borderRadius: 20,
              marginBottom: 10,
              padding: 4,
            }}>
              <TouchableOpacity
                onPress={() => setCameraMode('picture')}
                style={{
                  paddingHorizontal: 16,
                  paddingVertical: 8,
                  borderRadius: 16,
                  backgroundColor: cameraMode === 'picture' ? COLORS.primary : 'transparent',
                }}
              >
                <Text style={{ color: 'white', fontSize: 14, fontWeight: '500' }}>Photo</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => setCameraMode('video')}
                style={{
                  paddingHorizontal: 16,
                  paddingVertical: 8,
                  borderRadius: 16,
                  backgroundColor: cameraMode === 'video' ? COLORS.primary : 'transparent',
                }}
              >
                <Text style={{ color: 'white', fontSize: 14, fontWeight: '500' }}>Video</Text>
              </TouchableOpacity>
            </View>

            {/* Instruction Text */}
            <Text style={{
              color: 'rgba(255,255,255,0.8)',
              fontSize: 12,
              textAlign: 'center',
              marginBottom: 20,
            }}>
              {cameraMode === 'picture'
                ? 'Tap to take a photo'
                : isRecording
                  ? 'Tap to stop recording'
                  : 'Tap to start recording video'
              }
            </Text>

            {/* Capture Button */}
            <TouchableOpacity
              onPress={cameraMode === 'picture' ? takePicture : (isRecording ? stopRecording : startRecording)}
              disabled={isCapturing}
              style={{
                width: 80,
                height: 80,
                borderRadius: 40,
                backgroundColor: isRecording ? COLORS.error : 'white',
                borderWidth: 4,
                borderColor: isRecording ? 'white' : COLORS.primary,
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              {isCapturing ? (
                <ActivityIndicator size="small" color={COLORS.primary} />
              ) : (
                <Ionicons
                  name={cameraMode === 'picture' ? 'camera' : (isRecording ? 'stop' : 'videocam')}
                  size={32}
                  color={isRecording ? 'white' : COLORS.primary}
                />
              )}
            </TouchableOpacity>

            {isRecording && (
              <Text style={{
                color: COLORS.error,
                fontSize: 16,
                fontWeight: 'bold',
                marginTop: 10,
                textAlign: 'center'
              }}>
                Recording...
              </Text>
            )}
          </View>
        </CameraView>
      </View>
    );
  }

  return (
    <View style={{
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0,0,0,0.9)',
      zIndex: 1000,
      justifyContent: 'center',
      alignItems: 'center',
    }}>
      <View style={{
        backgroundColor: COLORS.surface,
        borderRadius: 12,
        padding: 20,
        width: '90%',
        maxWidth: 400,
      }}>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 20 }}>
          <Text style={{ color: COLORS.text, fontSize: 18, fontWeight: 'bold' }}>
            {isStory ? 'Create Story' : 'Create Update'}
          </Text>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color={COLORS.text} />
          </TouchableOpacity>
        </View>

        {!hasPermission ? (
          <View style={{ padding: 40, alignItems: 'center' }}>
            <Ionicons name="camera-outline" size={48} color={COLORS.textMuted} />
            <Text style={{ color: COLORS.textSecondary, marginTop: 16, textAlign: 'center' }}>
              Camera permission is required to take photos and videos
            </Text>
            <TouchableOpacity
              onPress={requestPermission}
              style={{
                backgroundColor: COLORS.primary,
                paddingHorizontal: 20,
                paddingVertical: 10,
                borderRadius: 8,
                marginTop: 16,
              }}
            >
              <Text style={{ color: 'white', fontWeight: 'bold' }}>Grant Permission</Text>
            </TouchableOpacity>
          </View>
        ) : isCapturing ? (
          <View style={{ padding: 40, alignItems: 'center' }}>
            <ActivityIndicator size="large" color={COLORS.primary} />
            <Text style={{ color: COLORS.textSecondary, marginTop: 10 }}>
              Opening camera...
            </Text>
          </View>
        ) : (
          <View>
            <Text style={{ color: COLORS.textSecondary, marginBottom: 20, textAlign: 'center' }}>
              Choose how you want to capture your {isStory ? 'story' : 'update'}
            </Text>

            {/* Photo Button */}
            <TouchableOpacity
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: COLORS.surfaceLight,
                padding: 15,
                borderRadius: 8,
                marginBottom: 10,
              }}
              onPress={openCameraForPhoto}
            >
              <Ionicons name="camera" size={24} color={COLORS.primary} />
              <Text style={{ color: COLORS.text, fontSize: 16, marginLeft: 15, fontWeight: '500' }}>
                Take Photo
              </Text>
            </TouchableOpacity>

            {/* Video Button */}
            <TouchableOpacity
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: COLORS.surfaceLight,
                padding: 15,
                borderRadius: 8,
                marginBottom: 10,
              }}
              onPress={openCameraForVideo}
            >
              <Ionicons name="videocam" size={24} color={COLORS.primary} />
              <Text style={{ color: COLORS.text, fontSize: 16, marginLeft: 15, fontWeight: '500' }}>
                Record Video
              </Text>
            </TouchableOpacity>

            {/* Removed Gallery Button - already available in avatar menu */}

            {/* Removed Cancel Button - X button already available in header */}
          </View>
        )}
      </View>
    </View>
  );
};

// Caption Input Modal Component
const CaptionInputModal = ({
  visible,
  mediaUri,
  mediaType,
  caption,
  onCaptionChange,
  onPost,
  onClose,
  isUploading,
  uploadProgress
}: any) => {
  // Handle Android back button
  React.useEffect(() => {
    if (!visible) return;

    const handleBackPress = () => {
      if (!isUploading) {
        onClose();
      }
      return true; // Prevent default back action
    };

    // Add back button listener for Android
    const backHandler = require('react-native').BackHandler;
    const subscription = backHandler?.addEventListener('hardwareBackPress', handleBackPress);

    return () => {
      subscription?.remove();
    };
  }, [visible, isUploading, onClose]);

  if (!visible) return null;

  return (
    <View style={{
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0,0,0,0.9)',
      zIndex: 1000,
      justifyContent: 'center',
      alignItems: 'center',
    }}>
      <View style={{
        backgroundColor: COLORS.surface,
        borderRadius: 12,
        padding: 20,
        width: '90%',
        maxWidth: 400,
        maxHeight: '80%',
      }}>
        <Text style={{ color: COLORS.text, fontSize: 18, fontWeight: 'bold', marginBottom: 20 }}>
          Add Caption
        </Text>

        {/* Media Preview */}
        {mediaUri && (
          <View style={{ marginBottom: 20, alignItems: 'center' }}>
            {mediaType === 'video' ? (
              <Video
                source={{ uri: mediaUri }}
                style={{ width: 200, height: 150, borderRadius: 8 }}
                resizeMode={ResizeMode.COVER}
                shouldPlay={false}
                useNativeControls={false}
              />
            ) : (
              <Image
                source={{ uri: mediaUri }}
                style={{ width: 200, height: 150, borderRadius: 8 }}
                resizeMode="cover"
              />
            )}
          </View>
        )}

        {/* Caption Input */}
        <TextInput
          style={{
            backgroundColor: COLORS.surfaceLight,
            color: COLORS.text,
            padding: 15,
            borderRadius: 8,
            marginBottom: 20,
            minHeight: 100,
            textAlignVertical: 'top',
          }}
          placeholder="Write a caption..."
          placeholderTextColor={COLORS.textMuted}
          value={caption}
          onChangeText={onCaptionChange}
          multiline
          maxLength={500}
        />

        {/* Upload Progress */}
        {isUploading && (
          <View style={{ marginBottom: 20 }}>
            <Text style={{ color: COLORS.textSecondary, marginBottom: 8 }}>
              Uploading... {Math.round(uploadProgress)}%
            </Text>
            <View style={{
              backgroundColor: COLORS.surfaceLight,
              height: 4,
              borderRadius: 2,
              overflow: 'hidden',
            }}>
              <View style={{
                backgroundColor: COLORS.primary,
                height: '100%',
                width: `${uploadProgress}%`,
              }} />
            </View>
          </View>
        )}

        {/* Action Buttons */}
        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          <TouchableOpacity
            style={{
              backgroundColor: COLORS.surfaceLight,
              padding: 15,
              borderRadius: 8,
              flex: 1,
              marginRight: 10,
            }}
            onPress={onClose}
            disabled={isUploading}
          >
            <Text style={{ color: COLORS.text, textAlign: 'center', fontWeight: 'bold' }}>
              Cancel
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={{
              backgroundColor: isUploading ? COLORS.textMuted : COLORS.primary,
              padding: 15,
              borderRadius: 8,
              flex: 1,
              marginLeft: 10,
            }}
            onPress={onPost}
            disabled={isUploading}
          >
            <Text style={{ color: 'white', textAlign: 'center', fontWeight: 'bold' }}>
              {isUploading ? 'Posting...' : 'Post'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

// ==================== AVATAR MENU MODAL ====================

interface AvatarMenuModalProps {
  visible: boolean;
  onClose: () => void;
  onTextStory: () => void;
  onCamera: () => void;
  onGallery: () => void;
  currentUser: any;
}

const AvatarMenuModal: React.FC<AvatarMenuModalProps> = ({
  visible,
  onClose,
  onTextStory,
  onCamera,
  onGallery,
  currentUser
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    // Animations removed for better performance
    fadeAnim.setValue(visible ? 1 : 0);
    scaleAnim.setValue(visible ? 1 : 0.8);
  }, [visible, fadeAnim, scaleAnim]);

  if (!visible) return null;

  const menuOptions = [
    {
      id: 'text',
      title: 'Text Story',
      subtitle: 'Share your thoughts',
      icon: 'text' as const,
      color: '#3B82F6',
      onPress: onTextStory,
    },
    {
      id: 'camera',
      title: 'Camera',
      subtitle: 'Take a photo or video',
      icon: 'camera' as const,
      color: '#10B981',
      onPress: onCamera,
    },
    {
      id: 'gallery',
      title: 'Gallery',
      subtitle: 'Choose from your photos',
      icon: 'images' as const,
      color: '#F59E0B',
      onPress: onGallery,
    },
  ];

  return (
    <View style={{
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 9999,
    }}>
      {/* Backdrop */}
      <Animated.View
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          opacity: fadeAnim,
        }}
      >
        <TouchableOpacity
          style={{ flex: 1 }}
          activeOpacity={1}
          onPress={onClose}
        />
      </Animated.View>

      {/* Menu Content */}
      <Animated.View
        style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: [
            { translateX: -150 },
            { translateY: -200 },
            { scale: scaleAnim },
          ],
          width: 300,
          backgroundColor: COLORS.surface,
          borderRadius: 20,
          padding: 24,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 10 },
          shadowOpacity: 0.3,
          shadowRadius: 20,
          elevation: 20,
          opacity: fadeAnim,
        }}
      >
        {/* Header */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginBottom: 20,
        }}>
          <View style={{
            width: 40,
            height: 40,
            borderRadius: 20,
            marginRight: 12,
            overflow: 'hidden',
          }}>
            {currentUser?.avatar ? (
              <Image
                source={{ uri: currentUser.avatar }}
                style={{ width: '100%', height: '100%' }}
              />
            ) : (
              <View style={{
                width: '100%',
                height: '100%',
                backgroundColor: COLORS.primary,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
                <Ionicons name="person" size={20} color="white" />
              </View>
            )}
          </View>
          <View style={{ flex: 1 }}>
            <Text style={{
              color: COLORS.text,
              fontSize: 18,
              fontWeight: '600',
            }}>
              Create Story
            </Text>
            <Text style={{
              color: COLORS.textSecondary,
              fontSize: 14,
            }}>
              Share your moment
            </Text>
          </View>
          <TouchableOpacity
            onPress={onClose}
            style={{
              width: 32,
              height: 32,
              borderRadius: 16,
              backgroundColor: COLORS.surfaceLight,
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Ionicons name="close" size={18} color={COLORS.textSecondary} />
          </TouchableOpacity>
        </View>

        {/* Menu Options */}
        {menuOptions.map((option, index) => (
          <TouchableOpacity
            key={option.id}
            onPress={option.onPress}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingVertical: 16,
              paddingHorizontal: 12,
              borderRadius: 12,
              marginBottom: index < menuOptions.length - 1 ? 8 : 0,
              backgroundColor: 'transparent',
            }}
            activeOpacity={0.7}
          >
            <View style={{
              width: 44,
              height: 44,
              borderRadius: 22,
              backgroundColor: `${option.color}20`,
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: 16,
            }}>
              <Ionicons name={option.icon} size={22} color={option.color} />
            </View>
            <View style={{ flex: 1 }}>
              <Text style={{
                color: COLORS.text,
                fontSize: 16,
                fontWeight: '500',
                marginBottom: 2,
              }}>
                {option.title}
              </Text>
              <Text style={{
                color: COLORS.textSecondary,
                fontSize: 13,
              }}>
                {option.subtitle}
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={18} color={COLORS.textMuted} />
          </TouchableOpacity>
        ))}
      </Animated.View>
    </View>
  );
};

// ==================== TEXT STORY MODAL ====================

interface TextStoryModalProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (text: string) => void;
  currentUser: any;
}

const TextStoryModal: React.FC<TextStoryModalProps> = ({
  visible,
  onClose,
  onSubmit,
  currentUser
}) => {
  const [text, setText] = useState('');
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    if (visible) {
      setText(''); // Reset text when modal opens
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.spring(slideAnim, {
          toValue: 0,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 50,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, fadeAnim, slideAnim]);

  const handleSubmit = () => {
    if (text.trim()) {
      onSubmit(text.trim());
      setText('');
    }
  };

  const handleClose = () => {
    setText('');
    onClose();
  };

  if (!visible) return null;

  return (
    <View style={{
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 9999,
    }}>
      {/* Backdrop */}
      <Animated.View
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          opacity: fadeAnim,
        }}
      >
        <TouchableOpacity
          style={{ flex: 1 }}
          activeOpacity={1}
          onPress={handleClose}
        />
      </Animated.View>

      {/* Modal Content */}
      <Animated.View
        style={{
          position: 'absolute',
          top: '20%',
          left: 20,
          right: 20,
          backgroundColor: COLORS.surface,
          borderRadius: 20,
          padding: 24,
          maxHeight: '60%',
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 10 },
          shadowOpacity: 0.3,
          shadowRadius: 20,
          elevation: 20,
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
        }}
      >
        {/* Header */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginBottom: 24,
        }}>
          <View style={{
            width: 40,
            height: 40,
            borderRadius: 20,
            marginRight: 12,
            overflow: 'hidden',
          }}>
            {currentUser?.avatar ? (
              <Image
                source={{ uri: currentUser.avatar }}
                style={{ width: '100%', height: '100%' }}
              />
            ) : (
              <View style={{
                width: '100%',
                height: '100%',
                backgroundColor: COLORS.primary,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
                <Ionicons name="person" size={20} color="white" />
              </View>
            )}
          </View>
          <View style={{ flex: 1 }}>
            <Text style={{
              color: COLORS.text,
              fontSize: 18,
              fontWeight: '600',
            }}>
              Create Text Story
            </Text>
            <Text style={{
              color: COLORS.textSecondary,
              fontSize: 14,
            }}>
              Share what's on your mind
            </Text>
          </View>
          <TouchableOpacity
            onPress={handleClose}
            style={{
              width: 32,
              height: 32,
              borderRadius: 16,
              backgroundColor: COLORS.surfaceLight,
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Ionicons name="close" size={18} color={COLORS.textSecondary} />
          </TouchableOpacity>
        </View>

        {/* Text Input */}
        <View style={{
          backgroundColor: COLORS.background,
          borderRadius: 12,
          padding: 16,
          marginBottom: 20,
          minHeight: 120,
        }}>
          <TextInput
            value={text}
            onChangeText={setText}
            placeholder="What's happening?"
            placeholderTextColor={COLORS.textMuted}
            multiline
            autoFocus
            style={{
              color: COLORS.text,
              fontSize: 16,
              lineHeight: 22,
              textAlignVertical: 'top',
              flex: 1,
            }}
            maxLength={500}
          />
        </View>

        {/* Character Count */}
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 20,
        }}>
          <Text style={{
            color: COLORS.textMuted,
            fontSize: 12,
          }}>
            {text.length}/500 characters
          </Text>
          <View style={{
            width: 40,
            height: 4,
            backgroundColor: COLORS.surfaceLight,
            borderRadius: 2,
            overflow: 'hidden',
          }}>
            <View style={{
              width: `${(text.length / 500) * 100}%`,
              height: '100%',
              backgroundColor: text.length > 450 ? '#EF4444' : COLORS.primary,
            }} />
          </View>
        </View>

        {/* Action Buttons */}
        <View style={{
          flexDirection: 'row',
          gap: 12,
        }}>
          <TouchableOpacity
            onPress={handleClose}
            style={{
              flex: 1,
              paddingVertical: 14,
              borderRadius: 12,
              backgroundColor: COLORS.surfaceLight,
              alignItems: 'center',
            }}
          >
            <Text style={{
              color: COLORS.textSecondary,
              fontSize: 16,
              fontWeight: '500',
            }}>
              Cancel
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={handleSubmit}
            disabled={!text.trim()}
            style={{
              flex: 1,
              paddingVertical: 14,
              borderRadius: 12,
              backgroundColor: text.trim() ? COLORS.primary : COLORS.surfaceLight,
              alignItems: 'center',
            }}
          >
            <Text style={{
              color: text.trim() ? 'white' : COLORS.textMuted,
              fontSize: 16,
              fontWeight: '600',
            }}>
              Share Story
            </Text>
          </TouchableOpacity>
        </View>
      </Animated.View>
    </View>
  );
};

// Removed duplicate styles object - using responsive styles inside component
