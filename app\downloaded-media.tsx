import { Ionicons } from "@expo/vector-icons";
import { useState, useEffect, useRef } from "react";
import {
  Alert,
  Animated,
  Dimensions,
  FlatList,
  Image,
  Modal,
  PanResponder,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { collection, query, where, orderBy, getDocs } from "firebase/firestore";
import { auth, db } from "../src/services/firebaseSimple";
import { navigationService } from "../src/services/navigationService";
import { FloatingActionButton } from "../src/components/NavigationHelper";
import { shareService } from "../src/services/shareService";

interface MediaItem {
  id: string;
  type: 'image' | 'video' | 'document';
  name: string;
  size: string;
  date: string;
  thumbnail: string;
  source: string; // Chat or group name
}

export default function DownloadedMediaScreen() {
  const [selectedTab, setSelectedTab] = useState<'all' | 'images' | 'videos' | 'documents'>('all');
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearchMode, setIsSearchMode] = useState(false);
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('grid');
  const [selectedMedia, setSelectedMedia] = useState<MediaItem | null>(null);
  const [showMediaViewer, setShowMediaViewer] = useState(false);

  // Header animation
  const scrollY = useRef(new Animated.Value(0)).current;
  const headerHeight = useRef(new Animated.Value(1)).current; // 1 = expanded, 0 = collapsed
  const lastScrollY = useRef(0);
  const scrollDirection = useRef<'up' | 'down'>('up');

  // Screen dimensions
  const screenWidth = Dimensions.get('window').width;
  const numColumns = viewMode === 'grid' ? 2 : 1;
  const itemSize = viewMode === 'grid' ? (screenWidth - 48) / 2 : screenWidth - 32;

  // Tab swipe navigation
  const tabOrder: ('all' | 'images' | 'videos' | 'documents')[] = ['all', 'images', 'videos', 'documents'];
  const contentTranslateX = useRef(new Animated.Value(0)).current;
  const tabIndicatorPosition = useRef(new Animated.Value(0)).current;

  // Load real downloaded media from Firebase
  useEffect(() => {
    loadRealMediaData();
  }, []);

  // Initialize tab indicator position
  useEffect(() => {
    const currentIndex = tabOrder.indexOf(selectedTab);
    tabIndicatorPosition.setValue(currentIndex);
  }, [selectedTab]);

  const loadDownloadedMedia = async () => {
    try {
      setLoading(true);

      // Get downloaded media from AsyncStorage
      const downloadedMediaJson = await AsyncStorage.getItem('downloaded_media');
      if (downloadedMediaJson) {
        const downloadedMedia = JSON.parse(downloadedMediaJson);
        setMediaItems(downloadedMedia);
      } else {
        // Initialize with empty array if no downloads yet
        setMediaItems([]);
      }
    } catch (error) {
      console.error('Error loading downloaded media:', error);
      setMediaItems([]);
    } finally {
      setLoading(false);
    }
  };

  // Save downloaded media to AsyncStorage
  const saveDownloadedMedia = async (newMediaItems: MediaItem[]) => {
    try {
      await AsyncStorage.setItem('downloaded_media', JSON.stringify(newMediaItems));
    } catch (error) {
      console.error('Error saving downloaded media:', error);
    }
  };





  // Load real media data from Firebase
  const loadRealMediaData = async () => {
    try {
      setLoading(true);

      // Get current user
      const currentUser = auth?.currentUser;
      if (!currentUser) {
        console.log('No authenticated user');
        setLoading(false);
        return;
      }

      // Query user's downloaded media from Firebase
      const mediaQuery = query(
        collection(db, 'shared_media'),
        where('downloadedBy', 'array-contains', currentUser.uid),
        orderBy('downloadedAt', 'desc')
      );

      const mediaSnapshot = await getDocs(mediaQuery);
      const realMediaData: MediaItem[] = mediaSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          type: data.type || 'document',
          name: data.fileName || 'Unknown File',
          size: data.fileSize || '0 KB',
          date: data.downloadedAt?.toDate().toISOString().split('T')[0] || new Date().toISOString().split('T')[0],
          thumbnail: data.thumbnailUrl || '',
          source: data.chatName || 'Unknown Chat'
        };
      });

      setMediaItems(realMediaData);
      console.log(`✅ Loaded ${realMediaData.length} real media items`);
    } catch (error) {
      console.error('❌ Error loading media data:', error);
      setMediaItems([]); // Show empty state instead of mock data
    } finally {
      setLoading(false);
    }
  };

  const filteredItems = mediaItems.filter(item => {
    // Filter by tab
    let matchesTab = true;
    if (selectedTab === 'images') matchesTab = item.type === 'image';
    else if (selectedTab === 'videos') matchesTab = item.type === 'video';
    else if (selectedTab === 'documents') matchesTab = item.type === 'document';

    // Filter by search query
    let matchesSearch = true;
    if (searchQuery.trim() !== '') {
      const query = searchQuery.toLowerCase();
      matchesSearch =
        item.name.toLowerCase().includes(query) ||
        item.source.toLowerCase().includes(query) ||
        item.type.toLowerCase().includes(query);
    }

    return matchesTab && matchesSearch;
  });

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image': return 'image-outline';
      case 'video': return 'videocam-outline';
      case 'document': return 'document-text-outline';
      default: return 'document-outline';
    }
  };



  const toggleItemSelection = (itemId: string) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        newSet.add(itemId);
      }

      // Exit selection mode if no items are selected
      if (newSet.size === 0) {
        setIsSelectionMode(false);
      }

      return newSet;
    });
  };

  const enterSelectionMode = (itemId: string) => {
    setIsSelectionMode(true);
    setSelectedItems(new Set([itemId]));
  };

  const exitSelectionMode = () => {
    setIsSelectionMode(false);
    setSelectedItems(new Set());
  };

  const selectAllItems = () => {
    const allItemIds = filteredItems.map(item => item.id);
    setSelectedItems(new Set(allItemIds));
  };

  const enterSearchMode = () => {
    setIsSearchMode(true);
    // Exit selection mode when entering search mode
    if (isSelectionMode) {
      exitSelectionMode();
    }
  };

  const exitSearchMode = () => {
    setIsSearchMode(false);
    setSearchQuery('');
    // Optional: Add a subtle animation when exiting search
    Animated.timing(contentTranslateX, {
      toValue: 5,
      duration: 100,
      useNativeDriver: true,
    }).start(() => {
      contentTranslateX.setValue(0);
    });
  };

  const handleSearchChange = (text: string) => {
    setSearchQuery(text);
    if (text.trim() === '') {
      exitSearchMode();
    }
  };

  // Media viewer functions
  const openMediaViewer = (item: MediaItem) => {
    setSelectedMedia(item);
    setShowMediaViewer(true);
  };

  const closeMediaViewer = () => {
    setShowMediaViewer(false);
    setSelectedMedia(null);
  };

  const toggleViewMode = () => {
    // Add a subtle animation when switching view modes
    Animated.sequence([
      Animated.timing(contentTranslateX, {
        toValue: 10,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(contentTranslateX, {
        toValue: 0,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    setViewMode(prev => prev === 'grid' ? 'list' : 'grid');
  };

  // Tab navigation functions
  const navigateToTab = (newTab: 'all' | 'images' | 'videos' | 'documents') => {
    setSelectedTab(newTab);

    // Instant tab indicator update
    const newIndex = tabOrder.indexOf(newTab);
    tabIndicatorPosition.setValue(newIndex);

    // Clear search when switching tabs
    if (isSearchMode) {
      exitSearchMode();
    }
  };

  const swipeToNextTab = () => {
    const currentIndex = tabOrder.indexOf(selectedTab);
    console.log(`Current tab: ${selectedTab}, index: ${currentIndex}`);

    if (currentIndex < tabOrder.length - 1) {
      const nextTab = tabOrder[currentIndex + 1];
      console.log(`Swiping LEFT to next tab: ${selectedTab} → ${nextTab}`);
      navigateToTab(nextTab);
    } else {
      console.log('Already at last tab, cannot swipe left further');
    }
  };

  const swipeToPreviousTab = () => {
    const currentIndex = tabOrder.indexOf(selectedTab);
    console.log(`Current tab: ${selectedTab}, index: ${currentIndex}`);

    if (currentIndex > 0) {
      const prevTab = tabOrder[currentIndex - 1];
      console.log(`Swiping RIGHT to previous tab: ${selectedTab} → ${prevTab}`);
      navigateToTab(prevTab);
    } else {
      console.log('Already at first tab, cannot swipe right further');
    }
  };

  // Create PanResponder for swipe detection
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => false, // Don't capture immediately
      onMoveShouldSetPanResponder: (_, gestureState) => {
        // Only respond to clear horizontal swipes
        const isHorizontal = Math.abs(gestureState.dx) > Math.abs(gestureState.dy) * 2;
        const hasMinDistance = Math.abs(gestureState.dx) > 15;
        return isHorizontal && hasMinDistance;
      },
      onPanResponderGrant: () => {
        // Stop any ongoing animations
        contentTranslateX.stopAnimation();
      },
      onPanResponderMove: (_, gestureState) => {
        // Smooth visual feedback
        contentTranslateX.setValue(gestureState.dx * 0.5);
      },
      onPanResponderRelease: (_, gestureState) => {
        // Reset visual feedback immediately
        contentTranslateX.setValue(0);

        // Lower threshold for instant response
        const swipeThreshold = 25;
        const velocity = gestureState.vx;

        console.log(`Gesture: dx=${gestureState.dx}, velocity=${velocity}`);

        // Check for swipe with distance or velocity
        if (gestureState.dx > swipeThreshold || velocity > 0.3) {
          // Swipe right - go to PREVIOUS tab (backward in order)
          console.log('Detected RIGHT swipe');
          swipeToPreviousTab();
        } else if (gestureState.dx < -swipeThreshold || velocity < -0.3) {
          // Swipe left - go to NEXT tab (forward in order)
          console.log('Detected LEFT swipe');
          swipeToNextTab();
        } else {
          console.log('Swipe too small, no navigation');
        }
      },
      onPanResponderTerminate: () => {
        contentTranslateX.setValue(0);
      },
    })
  ).current;

  // Handle scroll for header animation
  const handleScroll = Animated.event(
    [{ nativeEvent: { contentOffset: { y: scrollY } } }],
    {
      useNativeDriver: false,
      listener: (event: any) => {
        const currentScrollY = event.nativeEvent.contentOffset.y;
        const diff = currentScrollY - lastScrollY.current;

        // Determine scroll direction with improved logic
        // Don't collapse header during search mode or selection mode
        if (isSearchMode || isSelectionMode) {
          if (scrollDirection.current !== 'up') {
            scrollDirection.current = 'up';
            Animated.timing(headerHeight, {
              toValue: 1,
              duration: 200,
              useNativeDriver: false,
            }).start();
          }
        } else if (diff > 5 && currentScrollY > 80) {
          // Scrolling down with momentum and past threshold
          if (scrollDirection.current !== 'down') {
            scrollDirection.current = 'down';
            Animated.timing(headerHeight, {
              toValue: 0.3, // Don't fully collapse, keep some header visible
              duration: 250,
              useNativeDriver: false,
            }).start();
          }
        } else if (diff < -5 || currentScrollY <= 30) {
          // Scrolling up with momentum or near top
          if (scrollDirection.current !== 'up') {
            scrollDirection.current = 'up';
            Animated.timing(headerHeight, {
              toValue: 1,
              duration: 300,
              useNativeDriver: false,
            }).start();
          }
        }

        lastScrollY.current = currentScrollY;
      },
    }
  );

  const handleShareSelected = async () => {
    if (selectedItems.size === 0) {
      Alert.alert('No Selection', 'Please select media items to share first.');
      return;
    }

    const selectedMedia = mediaItems.filter(item => selectedItems.has(item.id));
    if (selectedMedia.length === 1) {
      const item = selectedMedia[0];
      const mediaType = item.type === 'image' ? 'image' : item.type === 'video' ? 'video' : 'document';
      await shareService.shareMedia(item.thumbnail, mediaType);
    } else {
      const urls = selectedMedia.map(item => item.thumbnail);
      const mediaType = selectedMedia[0].type === 'image' ? 'image' : selectedMedia[0].type === 'video' ? 'video' : 'document';
      await shareService.shareMultipleFiles(urls, mediaType);
    }
    exitSelectionMode();
  };

  const handleDeleteSelected = async () => {
    if (selectedItems.size === 0) return;

    const selectedCount = selectedItems.size;
    Alert.alert(
      "Delete Media",
      `Are you sure you want to delete ${selectedCount} item${selectedCount > 1 ? 's' : ''}?`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            const updatedItems = mediaItems.filter(item => !selectedItems.has(item.id));
            setMediaItems(updatedItems);
            await saveDownloadedMedia(updatedItems);
            exitSelectionMode();
            Alert.alert("Success", `${selectedCount} item${selectedCount > 1 ? 's' : ''} deleted`);
          }
        }
      ]
    );
  };

  const handleExportList = async () => {
    const exportData = mediaItems.map(item =>
      `${item.name} (${item.type}) - ${item.size} - Downloaded: ${item.date} - From: ${item.source}`
    ).join('\n');

    const exportText = `IraChat Downloaded Media List\nGenerated: ${new Date().toLocaleDateString()}\nTotal Items: ${mediaItems.length}\n\n${exportData}`;
    await shareService.shareText(exportText, { dialogTitle: 'Export Downloads List' });
  };

  const renderGridItem = ({ item }: { item: MediaItem }) => {
    const isSelected = selectedItems.has(item.id);

    return (
      <TouchableOpacity
        onPress={() => {
          if (isSelectionMode) {
            toggleItemSelection(item.id);
          } else {
            openMediaViewer(item);
          }
        }}
        onLongPress={() => {
          if (!isSelectionMode) {
            enterSelectionMode(item.id);
          }
        }}
        style={{
          width: itemSize,
          height: itemSize,
          margin: 8,
          borderRadius: 16,
          backgroundColor: isSelected ? '#E0F2FE' : '#FFFFFF',
          borderWidth: isSelected ? 3 : 0,
          borderColor: isSelected ? '#0EA5E9' : 'transparent',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.15,
          shadowRadius: 8,
          elevation: 6,
          opacity: isSelectionMode && !isSelected ? 0.6 : 1.0,
          overflow: 'hidden',
        }}
      >
        {/* Media Thumbnail */}
        <View style={{ flex: 1, position: 'relative' }}>
          {item.thumbnail ? (
            <Image
              source={{ uri: item.thumbnail }}
              style={{ width: '100%', height: '70%', borderTopLeftRadius: 16, borderTopRightRadius: 16 }}
              resizeMode="cover"
            />
          ) : (
            <View style={{
              width: '100%',
              height: '70%',
              backgroundColor: '#F3F4F6',
              alignItems: 'center',
              justifyContent: 'center',
              borderTopLeftRadius: 16,
              borderTopRightRadius: 16,
            }}>
              <Ionicons name={getFileIcon(item.type) as any} size={48} color="#667eea" />
            </View>
          )}

          {/* Type Badge Overlay */}
          <View style={{
            position: 'absolute',
            top: 8,
            right: 8,
            backgroundColor: item.type === 'image' ? '#3B82F6' :
                           item.type === 'video' ? '#F59E0B' : '#10B981',
            paddingHorizontal: 8,
            paddingVertical: 4,
            borderRadius: 12,
          }}>
            <Text style={{
              fontSize: 10,
              fontWeight: '700',
              color: '#FFFFFF',
            }}>
              {item.type.toUpperCase()}
            </Text>
          </View>

          {/* Selection Indicator */}
          {isSelected && (
            <View style={{
              position: 'absolute',
              top: 8,
              left: 8,
              width: 28,
              height: 28,
              borderRadius: 14,
              backgroundColor: '#0EA5E9',
              alignItems: 'center',
              justifyContent: 'center',
            }}>
              <Ionicons name="checkmark" size={18} color="#FFFFFF" />
            </View>
          )}
        </View>

        {/* File Info */}
        <View style={{
          height: '30%',
          padding: 12,
          justifyContent: 'center',
        }}>
          <Text style={{
            fontSize: 14,
            fontWeight: '600',
            color: '#374151',
            marginBottom: 4,
          }} numberOfLines={1}>
            {item.name}
          </Text>
          <Text style={{
            fontSize: 12,
            color: '#6B7280',
          }} numberOfLines={1}>
            {item.size} • {item.date}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderListItem = ({ item }: { item: MediaItem }) => {
    const isSelected = selectedItems.has(item.id);

    return (
      <TouchableOpacity
        onPress={() => {
          if (isSelectionMode) {
            toggleItemSelection(item.id);
          } else {
            openMediaViewer(item);
          }
        }}
        onLongPress={() => {
          if (!isSelectionMode) {
            enterSelectionMode(item.id);
          }
        }}
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          backgroundColor: isSelected ? '#E0F2FE' : '#FFFFFF',
          marginHorizontal: 16,
          marginVertical: 6,
          padding: 16,
          borderRadius: 16,
          borderWidth: isSelected ? 2 : 0,
          borderColor: isSelected ? '#0EA5E9' : 'transparent',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.12,
          shadowRadius: 6,
          elevation: 4,
          opacity: isSelectionMode && !isSelected ? 0.6 : 1.0,
        }}
      >
        {/* Enhanced Thumbnail */}
        <View style={{
          width: 80,
          height: 80,
          borderRadius: 12,
          backgroundColor: '#F3F4F6',
          alignItems: 'center',
          justifyContent: 'center',
          marginRight: 16,
          overflow: 'hidden',
        }}>
          {item.thumbnail ? (
            <Image
              source={{ uri: item.thumbnail }}
              style={{ width: 80, height: 80, borderRadius: 12 }}
              resizeMode="cover"
            />
          ) : (
            <Ionicons name={getFileIcon(item.type) as any} size={32} color="#667eea" />
          )}
        </View>

        {/* File Info */}
        <View style={{ flex: 1 }}>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#374151',
            marginBottom: 6,
          }} numberOfLines={1}>
            {item.name}
          </Text>
          <Text style={{
            fontSize: 14,
            color: '#6B7280',
            marginBottom: 4,
          }}>
            {item.size} • {item.date}
          </Text>
          <Text style={{
            fontSize: 12,
            color: '#9CA3AF',
          }}>
            From: {item.source}
          </Text>
        </View>

        {/* Selection Indicator */}
        {isSelected && (
          <View style={{
            width: 28,
            height: 28,
            borderRadius: 14,
            backgroundColor: '#0EA5E9',
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: 12,
          }}>
            <Ionicons name="checkmark" size={16} color="#FFFFFF" />
          </View>
        )}

        {/* Type Badge */}
        <View style={{
          backgroundColor: item.type === 'image' ? '#DBEAFE' :
                         item.type === 'video' ? '#FEF3C7' : '#ECFDF5',
          paddingHorizontal: 10,
          paddingVertical: 6,
          borderRadius: 12,
        }}>
          <Text style={{
            fontSize: 12,
            fontWeight: '600',
            color: item.type === 'image' ? '#3B82F6' :
                   item.type === 'video' ? '#F59E0B' : '#10B981',
          }}>
            {item.type.toUpperCase()}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const tabs = [
    { key: 'all', label: 'All', count: mediaItems.length },
    { key: 'images', label: 'Images', count: mediaItems.filter(i => i.type === 'image').length },
    { key: 'videos', label: 'Videos', count: mediaItems.filter(i => i.type === 'video').length },
    { key: 'documents', label: 'Docs', count: mediaItems.filter(i => i.type === 'document').length },
  ];

  return (
    <View style={{ flex: 1, backgroundColor: '#F0F9FF' }}>
      {/* Animated Header */}
      <Animated.View style={{
        backgroundColor: '#667eea',
        paddingTop: headerHeight.interpolate({
          inputRange: [0.3, 1],
          outputRange: [25, 55], // Collapsed: 25, Expanded: 55
          extrapolate: 'clamp',
        }),
        paddingBottom: headerHeight.interpolate({
          inputRange: [0.3, 1],
          outputRange: [6, 8], // Collapsed: 6, Expanded: 8
          extrapolate: 'clamp',
        }),
        paddingHorizontal: 20,
        overflow: 'hidden',
        elevation: headerHeight.interpolate({
          inputRange: [0.3, 1],
          outputRange: [2, 4], // Less shadow when collapsed
          extrapolate: 'clamp',
        }),
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: headerHeight.interpolate({
          inputRange: [0.3, 1],
          outputRange: [0.1, 0.2],
          extrapolate: 'clamp',
        }),
        shadowRadius: 4,
      }}>
        <Animated.View style={{
          transform: [{
            scale: headerHeight.interpolate({
              inputRange: [0.3, 1],
              outputRange: [0.9, 1], // Slightly smaller when collapsed
              extrapolate: 'clamp',
            })
          }]
        }}>
        {isSelectionMode ? (
          // Selection Mode Header
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <TouchableOpacity
                onPress={exitSelectionMode}
                style={{ marginRight: 16, padding: 8 }}
              >
                <Ionicons name="close" size={24} color="#FFFFFF" />
              </TouchableOpacity>
              <Text style={{
                fontSize: 18,
                fontWeight: 'bold',
                color: '#FFFFFF',
              }}>
                {selectedItems.size} Selected
              </Text>
            </View>

            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <TouchableOpacity
                onPress={selectAllItems}
                style={{ marginRight: 16, padding: 8 }}
              >
                <Ionicons name="checkmark-done" size={24} color="#FFFFFF" />
              </TouchableOpacity>

              <TouchableOpacity
                onPress={handleShareSelected}
                style={{ marginRight: 16, padding: 8 }}
              >
                <Ionicons name="share-outline" size={24} color="#FFFFFF" />
              </TouchableOpacity>

              <TouchableOpacity
                onPress={handleDeleteSelected}
                style={{ padding: 8 }}
              >
                <Ionicons name="trash-outline" size={24} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
          </View>
        ) : isSearchMode ? (
          // Search Mode Header
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <TouchableOpacity
              onPress={() => navigationService.goBack()}
              style={{ marginRight: 16, padding: 8 }}
            >
              <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
            </TouchableOpacity>

            <View style={{
              flex: 1,
              flexDirection: 'row',
              alignItems: 'center',
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              borderRadius: 25,
              paddingHorizontal: 16,
              paddingVertical: 8,
              marginRight: 8,
            }}>
              <Ionicons name="search" size={20} color="#FFFFFF" style={{ marginRight: 8 }} />
              <TextInput
                value={searchQuery}
                onChangeText={handleSearchChange}
                placeholder="Search media..."
                placeholderTextColor="rgba(255, 255, 255, 0.7)"
                style={{
                  flex: 1,
                  fontSize: 16,
                  color: '#FFFFFF',
                  paddingVertical: 4,
                }}
                autoFocus={true}
                returnKeyType="search"
              />
              {/* Always show close button in search mode */}
              <TouchableOpacity
                onPress={exitSearchMode}
                style={{
                  padding: 6,
                  marginLeft: 4,
                  borderRadius: 12,
                  backgroundColor: searchQuery.length > 0 ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
                }}
                activeOpacity={0.7}
              >
                <Ionicons
                  name={searchQuery.length > 0 ? "close-circle" : "close"}
                  size={18}
                  color="#FFFFFF"
                />
              </TouchableOpacity>
            </View>
          </View>
        ) : (
          // Normal Mode Header
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <TouchableOpacity
                onPress={() => navigationService.goBack()}
                style={{ marginRight: 16, padding: 8 }}
              >
                <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
              </TouchableOpacity>
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#FFFFFF',
              }}>
                Downloaded Media
              </Text>
            </View>

            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <TouchableOpacity
                onPress={toggleViewMode}
                style={{ padding: 8, marginRight: 8 }}
              >
                <Ionicons
                  name={viewMode === 'grid' ? 'list' : 'grid'}
                  size={24}
                  color="#FFFFFF"
                />
              </TouchableOpacity>

              <TouchableOpacity
                onPress={enterSearchMode}
                style={{ padding: 8 }}
              >
                <Ionicons name="search" size={24} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
          </View>
        )}
        </Animated.View>
      </Animated.View>

      {/* Tabs */}
      <View style={{
        flexDirection: 'row',
        backgroundColor: '#FFFFFF',
        paddingHorizontal: 8,
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#E5E7EB',
      }}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.key}
            onPress={() => navigateToTab(tab.key as any)}
            style={{
              flex: 1,
              alignItems: 'center',
              paddingVertical: 8,
              paddingHorizontal: 2,
              borderBottomWidth: 2,
              borderBottomColor: selectedTab === tab.key ? '#667eea' : 'transparent',
            }}
          >
            <Text style={{
              fontSize: 13,
              fontWeight: selectedTab === tab.key ? '600' : '500',
              color: selectedTab === tab.key ? '#667eea' : '#6B7280',
              textAlign: 'center',
            }} numberOfLines={1} adjustsFontSizeToFit={true}>
              {tab.label} ({tab.count})
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Media List with Swipe Navigation */}
      <View
        style={{ flex: 1 }}
        {...panResponder.panHandlers}
      >
        <Animated.View
          style={{
            flex: 1,
            transform: [{ translateX: contentTranslateX }]
          }}
        >
        <FlatList
          data={filteredItems}
          renderItem={viewMode === 'grid' ? renderGridItem : renderListItem}
          keyExtractor={(item) => item.id}
          numColumns={numColumns}
          key={viewMode} // Force re-render when view mode changes
          contentContainerStyle={{
            paddingVertical: 8,
            paddingHorizontal: viewMode === 'grid' ? 8 : 0,
          }}
          showsVerticalScrollIndicator={false}
          onScroll={handleScroll}
          scrollEventThrottle={16}
        ListEmptyComponent={
          <View style={{
            alignItems: 'center',
            justifyContent: 'center',
            paddingVertical: 80,
            paddingHorizontal: 40,
          }}>
            <View style={{
              width: 120,
              height: 120,
              borderRadius: 60,
              backgroundColor: '#F3F4F6',
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: 24,
            }}>
              <Ionicons
                name={searchQuery.trim() !== '' ? "search-outline" : "download-outline"}
                size={48}
                color="#9CA3AF"
              />
            </View>
            <Text style={{
              fontSize: 20,
              fontWeight: '700',
              color: '#374151',
              marginBottom: 12,
              textAlign: 'center',
            }}>
              {searchQuery.trim() !== '' ? "No Results Found" : "No Downloaded Media"}
            </Text>
            <Text style={{
              fontSize: 16,
              color: '#6B7280',
              textAlign: 'center',
              lineHeight: 24,
            }}>
              {searchQuery.trim() !== ''
                ? `No media found matching "${searchQuery}". Try a different search term.`
                : `Media files you download from chats will appear here in ${viewMode} view.`
              }
            </Text>
          </View>
        }
      />
        </Animated.View>
      </View>

      {/* Floating Action Button - Only show when not in selection mode */}
      {!isSelectionMode && (
        <FloatingActionButton
          actions={[
            {
              icon: 'document-text-outline',
              label: 'Export List',
              onPress: handleExportList,
              color: '#3B82F6',
            },
            {
              icon: 'trash-outline',
              label: 'Clear All',
              onPress: () => {
                Alert.alert(
                  "Clear All Downloads",
                  "Are you sure you want to delete all downloaded media?",
                  [
                    { text: "Cancel", style: "cancel" },
                    {
                      text: "Clear All",
                      style: "destructive",
                      onPress: async () => {
                        setMediaItems([]);
                        setSelectedItems(new Set());
                        await saveDownloadedMedia([]);
                        Alert.alert("Success", "All downloads cleared");
                      }
                    }
                  ]
                );
              },
              color: '#EF4444',
            },
          ]}
          mainAction={{
            icon: 'refresh-outline',
            onPress: loadDownloadedMedia,
            backgroundColor: '#667eea',
          }}
        />
      )}

      {/* Modern Media Viewer Modal */}
      <Modal
        visible={showMediaViewer}
        transparent={true}
        animationType="fade"
        onRequestClose={closeMediaViewer}
      >
        <View style={{
          flex: 1,
          backgroundColor: 'rgba(0, 0, 0, 0.95)',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
          {/* Header */}
          <View style={{
            position: 'absolute',
            top: 50,
            left: 0,
            right: 0,
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingHorizontal: 20,
            zIndex: 1000,
          }}>
            <TouchableOpacity
              onPress={closeMediaViewer}
              style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Ionicons name="close" size={24} color="#FFFFFF" />
            </TouchableOpacity>

            <View style={{ flexDirection: 'row' }}>
              <TouchableOpacity
                onPress={() => {
                  if (selectedMedia) {
                    // Share functionality
                    Alert.alert('Share', `Sharing ${selectedMedia.name}...`);
                  }
                }}
                style={{
                  width: 40,
                  height: 40,
                  borderRadius: 20,
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginLeft: 12,
                }}
              >
                <Ionicons name="share-outline" size={24} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Media Content */}
          {selectedMedia && (
            <View style={{
              width: '90%',
              height: '70%',
              borderRadius: 20,
              overflow: 'hidden',
              backgroundColor: '#FFFFFF',
            }}>
              {selectedMedia.thumbnail ? (
                <Image
                  source={{ uri: selectedMedia.thumbnail }}
                  style={{ width: '100%', height: '100%' }}
                  resizeMode="contain"
                />
              ) : (
                <View style={{
                  width: '100%',
                  height: '100%',
                  backgroundColor: '#F3F4F6',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                  <Ionicons name={getFileIcon(selectedMedia.type) as any} size={80} color="#667eea" />
                  <Text style={{
                    fontSize: 18,
                    fontWeight: '600',
                    color: '#374151',
                    marginTop: 16,
                  }}>
                    {selectedMedia.name}
                  </Text>
                </View>
              )}
            </View>
          )}

          {/* Media Info */}
          {selectedMedia && (
            <View style={{
              position: 'absolute',
              bottom: 50,
              left: 20,
              right: 20,
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              borderRadius: 16,
              padding: 20,
            }}>
              <Text style={{
                fontSize: 18,
                fontWeight: '700',
                color: '#374151',
                marginBottom: 8,
              }}>
                {selectedMedia.name}
              </Text>
              <Text style={{
                fontSize: 14,
                color: '#6B7280',
                marginBottom: 4,
              }}>
                Size: {selectedMedia.size} • Date: {selectedMedia.date}
              </Text>
              <Text style={{
                fontSize: 14,
                color: '#9CA3AF',
              }}>
                From: {selectedMedia.source}
              </Text>
            </View>
          )}
        </View>
      </Modal>
    </View>
  );
}
