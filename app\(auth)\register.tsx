import React, { useState, useEffect, useRef } from "react";
import {
    Alert,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    Text,

    View,
    StyleSheet,
    Animated,
    Dimensions,
} from "react-native";
import { StatusBar } from 'expo-status-bar';
import { useDispatch } from "react-redux";
import { useRouter, useLocalSearchParams } from "expo-router";
import PhoneNumberInput from "../../src/components/ui/PhoneNumberInput";
import ProfilePicturePicker from "../../src/components/ui/ProfilePicturePicker";
import { AnimatedInput } from "../../src/components/ui/AnimatedInput";
import { UsernameInput } from "../../src/components/ui/UsernameInput";
import { AnimatedButton } from "../../src/components/ui/AnimatedButton";
import { IraChatWallpaper } from "../../src/components/ui/IraChatWallpaper";
import { setUser } from "../../src/redux/userSlice";
import { createUserAccount, registerUser } from "../../src/services/authService";
import { IRACHAT_COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS, SHADOWS, ANIMATIONS } from "../../src/styles/iraChatDesignSystem";
import { AuthCredentials } from "../../src/types";

const { width: _screenWidth, height: _screenHeight } = Dimensions.get('window');

const RegisterScreen: React.FC = () => {
  const params = useLocalSearchParams();
  const verifiedPhone = params.verifiedPhone as string;
  const phoneVerified = params.phoneVerified === 'true';

  // Parse verified phone number to extract country code and phone number
  const parseVerifiedPhone = (fullPhone: string) => {
    if (!fullPhone) return { countryCode: "+256", phoneNumber: "" };

    // List of known country codes to match exactly
    const countryCodePatterns = [
      '+1', '+7', '+20', '+27', '+30', '+31', '+32', '+33', '+34', '+36', '+39', '+40', '+41', '+43', '+44', '+45', '+46', '+47', '+48', '+49',
      '+51', '+52', '+53', '+54', '+55', '+56', '+57', '+58', '+60', '+61', '+62', '+63', '+64', '+65', '+66', '+81', '+82', '+84', '+86', '+90', '+91', '+92', '+93', '+94', '+95', '+98',
      '+212', '+213', '+216', '+218', '+220', '+221', '+222', '+223', '+224', '+225', '+226', '+227', '+228', '+229', '+230', '+231', '+232', '+233', '+234', '+235', '+236', '+237', '+238', '+239', '+240', '+241', '+242', '+243', '+244', '+245', '+246', '+248', '+249', '+250', '+251', '+252', '+253', '+254', '+255', '+256', '+257', '+258', '+260', '+261', '+262', '+263', '+264', '+265', '+266', '+267', '+268', '+269', '+290', '+291', '+297', '+298', '+299',
      '+350', '+351', '+352', '+353', '+354', '+355', '+356', '+357', '+358', '+359', '+370', '+371', '+372', '+373', '+374', '+375', '+376', '+377', '+378', '+380', '+381', '+382', '+383', '+385', '+386', '+387', '+389', '+420', '+421', '+423', '+500', '+501', '+502', '+503', '+504', '+505', '+506', '+507', '+508', '+509', '+590', '+591', '+592', '+593', '+594', '+595', '+596', '+597', '+598', '+599',
      '+670', '+672', '+673', '+674', '+675', '+676', '+677', '+678', '+679', '+680', '+681', '+682', '+683', '+684', '+685', '+686', '+687', '+688', '+689', '+690', '+691', '+692', '+850', '+852', '+853', '+855', '+856', '+880', '+886', '+960', '+961', '+962', '+963', '+964', '+965', '+966', '+967', '+968', '+970', '+971', '+972', '+973', '+974', '+975', '+976', '+977', '+992', '+993', '+994', '+995', '+996', '+998'
    ];

    // Find the matching country code
    for (const code of countryCodePatterns) {
      if (fullPhone.startsWith(code)) {
        const phoneNumber = fullPhone.substring(code.length).replace(/\D/g, '');
        return { countryCode: code, phoneNumber };
      }
    }

    // Fallback: assume +256 if no match found
    return { countryCode: "+256", phoneNumber: fullPhone.replace(/\D/g, '') };
  };

  const { countryCode: initialCountryCode, phoneNumber: initialPhoneNumber } =
    parseVerifiedPhone(verifiedPhone);

  const [name, setName] = useState("");
  const [username, setUsername] = useState("@");
  const [phoneNumber, setPhoneNumber] = useState(initialPhoneNumber);
  const [countryCode, setCountryCode] = useState(initialCountryCode);
  const [bio, setBio] = useState("");
  const [bioLines, setBioLines] = useState(1);
  const [profilePicture, setProfilePicture] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const dispatch = useDispatch();
  const router = useRouter();

  // Animation refs
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const slideAnimation = useRef(new Animated.Value(30)).current;
  const formAnimation = useRef(new Animated.Value(0)).current;

  // Bio input handler with character limit and line calculation
  const handleBioChange = (text: string) => {
    // Limit to 150 characters
    if (text.length > 150) {
      return;
    }

    setBio(text);

    // Calculate number of lines more accurately
    if (text.length === 0) {
      setBioLines(1);
      return;
    }

    // Split text into lines based on 25 characters per line, considering word wrapping
    const words = text.split(' ');
    let currentLine = '';
    let lineCount = 1;

    for (const word of words) {
      const testLine = currentLine ? `${currentLine} ${word}` : word;

      if (testLine.length > 25) {
        if (currentLine) {
          // Start new line
          lineCount++;
          currentLine = word;
        } else {
          // Single word longer than 25 chars, force break
          lineCount += Math.ceil(word.length / 25);
          currentLine = '';
        }
      } else {
        currentLine = testLine;
      }
    }

    // Also count explicit line breaks
    const explicitLines = (text.match(/\n/g) || []).length + 1;

    setBioLines(Math.max(lineCount, explicitLines, 1));
  };

  useEffect(() => {
    // Beautiful entrance animations
    Animated.sequence([
      Animated.parallel([
        Animated.timing(fadeAnimation, {
          toValue: 1,
          duration: ANIMATIONS.slow,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnimation, {
          toValue: 0,
          duration: ANIMATIONS.slow,
          useNativeDriver: true,
        }),
      ]),
      Animated.spring(formAnimation, {
        toValue: 1,
        tension: 50,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnimation, formAnimation, slideAnimation]);

  // Create account directly without SMS verification
  const createAccount = async () => {

    // Clear any previous errors
    setError("");

    // Validation - Name can contain letters, numbers, emojis, and spaces
    if (!name.trim()) {
      const errorMsg = "Please enter your name";
      setError(errorMsg);
      Alert.alert("Error", errorMsg);
      return;
    }

    if (name.trim().length < 2) {
      const errorMsg = "Name must be at least 2 characters long";
      setError(errorMsg);
      Alert.alert("Error", errorMsg);
      return;
    }

    // Allow Unicode characters including emojis, letters, numbers, and spaces
    const nameRegex = /^[\p{L}\p{N}\p{Emoji}\s._-]+$/u;
    if (!nameRegex.test(name.trim())) {
      const errorMsg = "Name can contain letters, numbers, emojis, spaces, and basic punctuation";
      setError(errorMsg);
      Alert.alert("Error", errorMsg);
      return;
    }

    if (!username.trim()) {
      const errorMsg = "Please enter a username";
      setError(errorMsg);
      Alert.alert("Error", errorMsg);
      return;
    }

    if (username.trim().length < 3) {
      const errorMsg = "Username must be at least 3 characters long";
      setError(errorMsg);
      Alert.alert("Error", errorMsg);
      return;
    }

    // Username validation - system will automatically add @ symbol
    // Username is now stored without @ symbol, system adds it automatically
    let cleanUsernameForValidation = username;

    // Check minimum length
    if (cleanUsernameForValidation.length < 3) {
      const errorMsg = "Username must be at least 3 characters long";
      setError(errorMsg);
      Alert.alert("Error", errorMsg);
      return;
    }

    // Check maximum length - strictly not more than 12 characters
    if (cleanUsernameForValidation.length > 12) {
      const errorMsg = "Username cannot be more than 12 characters long";
      setError(errorMsg);
      Alert.alert("Error", errorMsg);
      return;
    }

    // Username must be one word (no spaces)
    if (/\s/.test(cleanUsernameForValidation)) {
      const errorMsg = "Username cannot contain spaces";
      setError(errorMsg);
      Alert.alert("Error", errorMsg);
      return;
    }

    // Allow any characters except spaces - no other restrictions
    const usernameRegex = /^[^\s]+$/u;
    if (!usernameRegex.test(cleanUsernameForValidation)) {
      const errorMsg = "Username cannot contain spaces";
      setError(errorMsg);
      Alert.alert("Error", errorMsg);
      return;
    }

    if (!phoneNumber.trim()) {
      const errorMsg = "Please enter your phone number";
      setError(errorMsg);
      Alert.alert("Error", errorMsg);
      return;
    }

    const fullPhoneNumber = countryCode + phoneNumber.replace(/\D/g, "");

    setLoading(true);
    try {
      // Create account using auth service
      // Username is already clean (without @), system will add @ symbol automatically
      const cleanUsername = username;

      const result = await createUserAccount(fullPhoneNumber, {
        name: name.trim(),
        username: cleanUsername.trim(),
        bio: bio.trim() || "I Love IraChat",
        avatar: profilePicture,
      });

      if (!result.success || !result.user) {
        throw new Error(result.message);
      }

      // Update Redux store immediately
      dispatch(setUser(result.user));

      // Clear form
      setName("");
      setUsername("");
      setPhoneNumber("");
      setBio("");
      setProfilePicture("");
      setError("");

      // Force navigation to tabs immediately after successful registration
      setTimeout(() => {
        router.replace("/(tabs)");
      }, 500);
    } catch (error: any) {

      // Show specific error messages for phone/username conflicts
      if (error.message.includes("phone number is already registered")) {
        const errorMessage =
          "This phone number is already registered. Each phone number can only have one account for security reasons.";
        setError(errorMessage);
        Alert.alert(
          "Phone Number Already Registered",
          "This phone number is already associated with an account. Each phone number can only have one account for security reasons.\n\nIf this is your phone number, please contact support.",
          [{ text: "OK", style: "default" }],
        );
      } else if (error.message.includes("username is already taken")) {
        const errorMessage =
          "This username is already taken. Please choose a different username.";
        setError(errorMessage);
        Alert.alert(
          "Username Taken",
          "This username is already taken. Please choose a different username.",
          [{ text: "OK", style: "default" }],
        );
      } else {
        // Show the actual error message for debugging
        const errorMessage =
          error.message || "Failed to create account. Please try again.";
        setError(errorMessage);
        Alert.alert("Error", `Registration failed: ${errorMessage}`);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" />

      {/* Beautiful animated wallpaper */}
      <IraChatWallpaper variant="auto" animated={true} />

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          bounces={true}
        >
          <Animated.View
            style={[
              styles.content,
              {
                opacity: fadeAnimation,
                transform: [{ translateY: slideAnimation }],
              },
            ]}
          >
            {/* Header */}
            <Animated.View
              style={[
                styles.header,
                {
                  transform: [{ scale: formAnimation }],
                },
              ]}
            >
              <Text style={styles.title}>Create Account</Text>
              <Text style={styles.subtitle}>
                Join IraChat and connect with friends through beautiful messaging
              </Text>
            </Animated.View>
            {/* Error Message */}
            {error && (
              <Animated.View style={styles.errorContainer}>
                <Text style={styles.errorText}>{error}</Text>
              </Animated.View>
            )}

            {/* Registration Form */}
            <Animated.View
              style={[
                styles.formContainer,
                {
                  transform: [{ scale: formAnimation }],
                },
              ]}
            >
              {/* Profile Picture Section */}
              <View style={styles.profileSection}>
                <Text style={styles.sectionTitle}>Profile Picture</Text>
                <ProfilePicturePicker
                  onImageSelect={setProfilePicture}
                  currentImage={profilePicture}
                  size={80} // Reduced size for compact layout
                />
              </View>

              {/* Form Fields */}
              <View style={styles.fieldsContainer}>
                <AnimatedInput
                  label="Full Name"
                  placeholder="Enter your full name"
                  value={name}
                  onChangeText={setName}
                  icon="person"
                  size="small"
                  containerStyle={styles.compactInput}
                />

                <UsernameInput
                  label="Username"
                  placeholder="Choose a unique username"
                  value={username}
                  onChangeText={setUsername}
                  size="small"
                />

                <View style={styles.phoneSection}>
                  <Text style={styles.fieldLabel}>
                    Phone Number {phoneVerified && "✅ Verified"}
                  </Text>
                  <PhoneNumberInput
                    value={phoneNumber}
                    placeholder={phoneVerified ? "Phone Number (Verified)" : "Phone Number"}
                    onChangeText={phoneVerified ? () => {} : setPhoneNumber}
                    onCountryChange={phoneVerified ? () => {} : setCountryCode}
                    editable={!phoneVerified}
                    initialCountryCode={countryCode}
                  />
                </View>

                <View style={styles.bioContainer}>
                  <AnimatedInput
                    label="Bio (Optional)"
                    placeholder="Tell us about yourself (max 150 chars)"
                    value={bio}
                    onChangeText={handleBioChange}
                    icon="information-circle"
                    variant="outlined"
                    size="small"
                    multiline={true}
                    numberOfLines={bioLines}
                    textAlignVertical="top"
                    containerStyle={styles.compactInput}
                  />
                  <Text style={[
                    styles.charCounter,
                    bio.length > 120 && styles.charCounterWarning,
                    bio.length >= 150 && styles.charCounterLimit
                  ]}>
                    {bio.length}/150 characters
                  </Text>
                </View>
              </View>

              {/* Create Account Button */}
              <View style={styles.buttonContainer}>
                <AnimatedButton
                  title={loading ? "Creating Account..." : "Create Account"}
                  onPress={createAccount}
                  variant="gradient"
                  size="small"
                  icon="person-add"
                  iconPosition="left"
                  fullWidth={true}
                  disabled={loading}
                  loading={loading}
                  style={styles.createButton}
                />
              </View>
            </Animated.View>
          </Animated.View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

export default RegisterScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#87CEEB',
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: SPACING.sm,
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.sm,
    paddingTop: SPACING.md,
  },
  header: {
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  title: {
    fontSize: TYPOGRAPHY.fontSize['2xl'],
    fontWeight: TYPOGRAPHY.fontWeight.bold as any,
    color: IRACHAT_COLORS.textOnPrimary,
    textAlign: 'center',
    marginBottom: SPACING.xs,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  subtitle: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: IRACHAT_COLORS.textOnPrimary,
    textAlign: 'center',
    opacity: 0.9,
    lineHeight: TYPOGRAPHY.lineHeight.normal * TYPOGRAPHY.fontSize.sm,
    paddingHorizontal: SPACING.sm,
  },
  errorContainer: {
    marginBottom: SPACING.sm,
    padding: SPACING.sm,
    backgroundColor: IRACHAT_COLORS.surface,
    borderWidth: 1,
    borderColor: IRACHAT_COLORS.error,
    borderRadius: BORDER_RADIUS.lg,
    ...SHADOWS.sm,
  },
  errorText: {
    color: IRACHAT_COLORS.error,
    textAlign: 'center',
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: TYPOGRAPHY.fontWeight.medium as any,
  },
  formContainer: {
    backgroundColor: IRACHAT_COLORS.surface,
    borderRadius: BORDER_RADIUS.xl,
    padding: SPACING.md,
    ...SHADOWS.lg,
  },
  profileSection: {
    alignItems: 'center',
    marginBottom: SPACING.sm,
    paddingVertical: SPACING.sm,
    backgroundColor: IRACHAT_COLORS.backgroundDark,
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 2,
    borderColor: IRACHAT_COLORS.borderLight,
    borderStyle: 'dashed',
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.fontSize.base,
    fontWeight: TYPOGRAPHY.fontWeight.semibold as any,
    color: IRACHAT_COLORS.text,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  fieldsContainer: {
    marginBottom: SPACING.sm,
  },
  phoneSection: {
    marginVertical: SPACING.xs,
  },
  fieldLabel: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    fontWeight: TYPOGRAPHY.fontWeight.semibold as any,
    color: IRACHAT_COLORS.text,
    marginBottom: 2,
  },
  buttonContainer: {
    marginTop: SPACING.sm,
  },
  createButton: {
    marginTop: SPACING.md,
  },
  compactInput: {
    marginVertical: SPACING.xs / 2, // Even more compact spacing
  },
  bioContainer: {
    marginVertical: SPACING.xs / 2,
  },
  charCounter: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    color: IRACHAT_COLORS.textMuted,
    textAlign: 'right',
    marginTop: 2,
    marginRight: SPACING.sm,
  },
  charCounterWarning: {
    color: IRACHAT_COLORS.warning,
  },
  charCounterLimit: {
    color: IRACHAT_COLORS.error,
    fontWeight: TYPOGRAPHY.fontWeight.semibold as any,
  },
});
