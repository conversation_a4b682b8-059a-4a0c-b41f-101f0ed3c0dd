// ⚙️ MAIN SETTINGS PAGE - DRAMATICALLY REDESIGNED WITH BRIGHT ORANGE HEADER
// This is the PRIMARY settings page accessed from the main menu overlay
// Features: Bright orange header, no bottom tab bar, edge-to-edge design
// Real profile editing, privacy settings, notifications, and app preferences

import { Ionicons } from "@expo/vector-icons";

import React, { useCallback, useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  StyleSheet,
  Switch,
  Text,
  TouchableOpacity,
  View,
  Animated,
} from "react-native";
import { useSelector } from "react-redux";
import { RootState } from "../src/redux/store";
import { realSettingsService, UserSettings } from "../src/services/realSettingsService";
import { navigationService, ROUTES } from "../src/services/navigationService";
import { ResponsiveListCard } from "../src/components/ui/ResponsiveCard";
import { IRACHAT_COLORS, TYPOGRAPHY, BORDER_RADIUS, SHADOWS } from "../src/styles/iraChatDesignSystem";
import { ResponsiveTypography, ResponsiveSpacing, DeviceInfo } from "../src/utils/responsiveUtils";

export default function SettingsScreen() {
  const currentUser = useSelector((state: RootState) => state.user.currentUser);

  const [settings, setSettings] = useState<UserSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Animation values
  const fadeAnim = useState(new Animated.Value(0))[0];
  const slideAnim = useState(new Animated.Value(50))[0];
  const responsiveTypography = ResponsiveTypography;
  const responsiveSpacing = ResponsiveSpacing;

  // Load settings on component mount
  // Load user settings
  const loadSettings = useCallback(async () => {
    if (!currentUser?.phoneNumber) return;

    try {
      setIsLoading(true);
      const userSettings = await realSettingsService.getUserSettings(currentUser.phoneNumber);
      setSettings(userSettings);



      // Start animations
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]).start();
    } catch (error) {
      console.error('Failed to load settings:', error);
      Alert.alert('Error', 'Failed to load settings. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [fadeAnim, slideAnim]);

  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  // Update setting
  const updateSetting = async (key: keyof UserSettings, value: any) => {
    if (!settings || !currentUser?.phoneNumber) return;

    try {
      const updatedSettings = { ...settings, [key]: value };
      await realSettingsService.updateUserSettings(currentUser.phoneNumber, { [key]: value });
      setSettings(updatedSettings);
    } catch (error) {
      console.error('Failed to update setting:', error);
      Alert.alert('Error', 'Failed to update setting. Please try again.');
    }
  };



  // Handle privacy lock settings
  const handlePrivacyLockSettings = () => {
    Alert.alert(
      "Privacy Lock Settings",
      "Configure your app lock preferences",
      [
        { text: "Cancel", style: "cancel" },
        { text: "Set PIN", onPress: () => console.log("Set PIN pressed") },
        { text: "Use Biometrics", onPress: () => console.log("Biometrics pressed") }
      ]
    );
  };

  // Handle lock app
  const handleLockApp = () => {
    try {
      Alert.alert("App Locked", "The app has been locked for privacy.");
    } catch (error) {
      console.error('Failed to lock app:', error);
      Alert.alert("Error", "Failed to lock app. Please try again.");
    }
  };

  // Render setting item
  const renderSettingItem = (
    icon: string,
    title: string,
    subtitle?: string,
    onPress?: () => void,
    rightElement?: React.ReactNode
  ) => (
    <TouchableOpacity style={styles.settingItem} onPress={onPress}>
      <View style={styles.settingLeft}>
        <Ionicons name={icon as any} size={24} color={IRACHAT_COLORS.primary} style={styles.settingIcon} />
        <View style={styles.settingText}>
          <Text style={styles.settingTitle}>{title}</Text>
          {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
        </View>
      </View>
      {rightElement || <Ionicons name="chevron-forward" size={20} color={IRACHAT_COLORS.textSecondary} />}
    </TouchableOpacity>
  );

  // Render switch setting
  const renderSwitchSetting = (
    icon: string,
    title: string,
    subtitle: string,
    value: boolean,
    onValueChange: (_value: boolean) => void
  ) => (
    <View style={styles.settingItem}>
      <View style={styles.settingLeft}>
        <Ionicons name={icon as any} size={24} color={IRACHAT_COLORS.primary} style={styles.settingIcon} />
        <View style={styles.settingText}>
          <Text style={styles.settingTitle}>{title}</Text>
          <Text style={styles.settingSubtitle}>{subtitle}</Text>
        </View>
      </View>
      <Switch
        value={value}
        onValueChange={onValueChange}
        trackColor={{ false: IRACHAT_COLORS.border, true: IRACHAT_COLORS.primary }}
        thumbColor={IRACHAT_COLORS.surface}
      />
    </View>
  );

  return (
    <View style={styles.fullScreenContainer}>
      {/* Status bar background to match header */}
      <View style={styles.statusBarBackground} />

      {/* COMPLETELY NEW DRAMATIC HEADER DESIGN */}
      <View style={styles.dramaticHeader}>
        {/* Decorative background pattern */}
        <View style={styles.headerPattern} />

        <View style={styles.headerContent}>
          <TouchableOpacity
            onPress={() => navigationService.goBack()}
            style={styles.newBackButton}
          >
            <Ionicons
              name="arrow-back"
              size={28}
              color="white"
            />
          </TouchableOpacity>
          <View style={styles.titleContainer}>
            <Animated.Text style={[styles.dramaticTitle, { opacity: fadeAnim }]}>
              ⚙️ SETTINGS
            </Animated.Text>
            <Text style={styles.subtitle}>Customize your experience</Text>
            <View style={styles.titleUnderline} />
          </View>
          <View style={styles.rightSpacer} />
        </View>

        {/* Bottom wave decoration */}
        <View style={styles.waveDecoration} />
      </View>

      <Animated.ScrollView
        style={[styles.content, {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }]}
        showsVerticalScrollIndicator={false}
      >

        {/* Show loading state or content */}
        {isLoading || !settings ? (
          <View style={styles.contentLoadingContainer}>
            <ActivityIndicator size="large" color={IRACHAT_COLORS.primary} />
            <Text style={styles.loadingText}>
              {isLoading ? 'Loading settings...' : 'Initializing settings...'}
            </Text>
          </View>
        ) : (
          <>
            {/* Notifications Section */}
            <ResponsiveListCard
              style={{
                marginTop: responsiveSpacing.md,
                borderRadius: BORDER_RADIUS.lg,
                ...SHADOWS.sm,
                backgroundColor: IRACHAT_COLORS.surface
              }}
            >
              <Text style={{
                fontSize: responsiveTypography.fontSize.sm,
                fontWeight: '600' as const,
                color: IRACHAT_COLORS.textSecondary,
                paddingHorizontal: responsiveSpacing.md, // Add back padding for content readability
                paddingVertical: responsiveSpacing.sm,
                backgroundColor: IRACHAT_COLORS.backgroundDark,
                textTransform: 'uppercase',
                letterSpacing: 0.5,
                fontFamily: TYPOGRAPHY.fontFamily,
              }}>Notifications</Text>

              {renderSwitchSetting(
                "notifications-outline",
                "Message Notifications",
                "Get notified about new messages",
                settings.messageNotifications,
                (value) => updateSetting('messageNotifications', value)
              )}

          {renderSwitchSetting(
            "call-outline",
            "Call Notifications",
            "Get notified about incoming calls",
            settings.callNotifications,
            (value) => updateSetting('callNotifications', value)
          )}

          {renderSwitchSetting(
            "people-outline",
            "Group Notifications",
            "Get notified about group messages",
            settings.groupNotifications,
            (value) => updateSetting('groupNotifications', value)
          )}

          {renderSwitchSetting(
            "volume-high-outline",
            "Sound & Vibration",
            "Enable sound and vibration for notifications",
            settings.vibrationEnabled,
            (value) => updateSetting('vibrationEnabled', value)
          )}
        </ResponsiveListCard>

        {/* Chat Settings Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Chat Settings</Text>
          
          {renderSwitchSetting(
            "checkmark-done-outline",
            "Read Receipts",
            "Let others know when you've read their messages",
            settings.readReceiptsEnabled,
            (value) => updateSetting('readReceiptsEnabled', value)
          )}
          
          {renderSettingItem(
            "text-outline",
            "Font Size",
            `Currently: ${settings.fontSize}`,
            () => {
              // Show font size picker
              Alert.alert(
                "Font Size",
                "Choose your preferred font size",
                [
                  { text: "Small", onPress: () => updateSetting('fontSize', 'small') },
                  { text: "Medium", onPress: () => updateSetting('fontSize', 'medium') },
                  { text: "Large", onPress: () => updateSetting('fontSize', 'large') },
                  { text: "Cancel", style: "cancel" }
                ]
              );
            }
          )}
          
          {renderSettingItem(
            "color-palette-outline",
            "Theme",
            `Currently: ${settings.theme}`,
            () => {
              // Show theme picker
              Alert.alert(
                "Theme",
                "Choose your preferred theme",
                [
                  { text: "Light", onPress: () => updateSetting('theme', 'light') },
                  { text: "Dark", onPress: () => updateSetting('theme', 'dark') },
                  { text: "Auto", onPress: () => updateSetting('theme', 'auto') },
                  { text: "Cancel", style: "cancel" }
                ]
              );
            }
          )}
        </View>

        {/* Storage & Data Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Storage & Data</Text>
          
          {renderSwitchSetting(
            "cloud-upload-outline",
            "Auto Backup",
            "Automatically backup your chats",
            settings.autoBackup,
            (value) => updateSetting('autoBackup', value)
          )}
          
          {renderSettingItem(
            "download-outline",
            "Auto Download Media",
            `Currently: ${settings.autoDownloadMedia}`,
            () => {
              Alert.alert(
                "Auto Download Media",
                "Choose when to automatically download media",
                [
                  { text: "Never", onPress: () => updateSetting('autoDownloadMedia', 'never') },
                  { text: "Wi-Fi Only", onPress: () => updateSetting('autoDownloadMedia', 'wifi') },
                  { text: "Always", onPress: () => updateSetting('autoDownloadMedia', 'always') },
                  { text: "Cancel", style: "cancel" }
                ]
              );
            }
          )}
          
          {renderSettingItem(
            "archive-outline",
            "Export Chat History",
            "Download your chat history",
            () => navigationService.navigate(ROUTES.SETTINGS.EXPORT_DATA)
          )}
        </View>

        {/* Support Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Support</Text>
          
          {renderSettingItem(
            "help-circle-outline",
            "Help & Support",
            "Get help and contact support",
            () => navigationService.navigate(ROUTES.HELP.HELP)
          )}

          {renderSettingItem(
            "information-circle-outline",
            "About",
            "App version and information",
            () => navigationService.navigate(ROUTES.HELP.ABOUT)
          )}
        </View>

        {/* Privacy & Security Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Privacy & Security</Text>

          {renderSettingItem(
            "shield-checkmark-outline",
            "Privacy Lock Settings",
            "Set up PIN/password to protect your app",
            handlePrivacyLockSettings,
            <Ionicons name="shield-checkmark-outline" size={20} color={IRACHAT_COLORS.primary} />
          )}

          {renderSettingItem(
            "lock-closed-outline",
            "Lock App Now",
            "Immediately lock the app for privacy",
            handleLockApp,
            <Ionicons name="lock-closed-outline" size={20} color={IRACHAT_COLORS.textSecondary} />
          )}
        </View>
        </>
        )}
      </Animated.ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  fullScreenContainer: {
    flex: 1,
    backgroundColor: IRACHAT_COLORS.background,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 9999, // Ensure it's above any tab navigation
  },
  statusBarBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: DeviceInfo.statusBarHeight,
    backgroundColor: IRACHAT_COLORS.primary, // Use app's primary color
    zIndex: 10000,
  },
  container: {
    flex: 1,
    backgroundColor: IRACHAT_COLORS.background,
  },
  // COMPLETELY NEW DRAMATIC HEADER STYLES
  dramaticHeader: {
    backgroundColor: IRACHAT_COLORS.primary, // Match status bar color
    paddingTop: DeviceInfo.statusBarHeight,
    paddingBottom: ResponsiveSpacing.lg,
    borderBottomLeftRadius: 25,
    borderBottomRightRadius: 25,
    shadowColor: IRACHAT_COLORS.primary,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 15,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: ResponsiveSpacing.lg,
    paddingTop: ResponsiveSpacing.md,
  },
  newBackButton: {
    width: 50,
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  titleContainer: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: ResponsiveSpacing.md,
  },
  dramaticTitle: {
    fontSize: ResponsiveTypography.fontSize['2xl'],
    fontWeight: '900',
    color: 'white',
    textAlign: 'center',
    letterSpacing: 2,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  subtitle: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginTop: 4,
    fontWeight: '500',
  },
  titleUnderline: {
    width: 60,
    height: 3,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 2,
    marginTop: 8,
    alignSelf: 'center',
  },
  rightSpacer: {
    width: 50, // Same as back button to center the title
  },
  headerPattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    opacity: 0.3,
  },
  waveDecoration: {
    position: 'absolute',
    bottom: -1,
    left: 0,
    right: 0,
    height: 20,
    backgroundColor: IRACHAT_COLORS.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },

  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentLoadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: ResponsiveSpacing.xl * 2,
    marginTop: ResponsiveSpacing.lg,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: IRACHAT_COLORS.textSecondary,
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: IRACHAT_COLORS.surface,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: IRACHAT_COLORS.textSecondary,
    paddingHorizontal: ResponsiveSpacing.md, // Add back padding for content readability
    paddingVertical: 12,
    backgroundColor: IRACHAT_COLORS.backgroundDark,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: ResponsiveSpacing.md, // Add back padding for content readability
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: IRACHAT_COLORS.borderLight,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    marginRight: 16,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: IRACHAT_COLORS.text,
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    color: IRACHAT_COLORS.textSecondary,
  },
});
