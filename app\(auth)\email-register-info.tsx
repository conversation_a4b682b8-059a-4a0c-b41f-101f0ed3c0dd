// Email Registration Info Screen for IraChat
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useState } from 'react';
import {
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
  Image,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';

export default function EmailRegisterInfoScreen() {
  const router = useRouter();
  const [isNavigating, setIsNavigating] = useState(false);

  const handleContinue = () => {
    if (isNavigating) return;
    setIsNavigating(true);
    router.push('/(auth)/email-register');
    setTimeout(() => setIsNavigating(false), 1000);
  };

  const handleBack = () => {
    if (isNavigating) return;
    setIsNavigating(true);
    router.back();
    setTimeout(() => setIsNavigating(false), 1000);
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#87CEEB' }}>
      <StatusBar style="light" backgroundColor="#87CEEB" />
      
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        {/* Header */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingHorizontal: 20,
          paddingVertical: 16,
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
        }}>
          <TouchableOpacity
            onPress={handleBack}
            disabled={isNavigating}
            style={{ padding: 8 }}
          >
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          
          <Text style={{
            fontSize: 20,
            fontWeight: '600',
            color: '#FFFFFF',
            textAlign: 'center',
          }}>
            Create Account with Email
          </Text>
          
          <View style={{ width: 40 }} />
        </View>

        <ScrollView style={{ flex: 1 }} contentContainerStyle={{ padding: 24 }}>
          {/* IraChat Logo */}
          <View style={{ alignItems: 'center', marginBottom: 40 }}>
            <View style={{
              width: 120,
              height: 120,
              borderRadius: 60,
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: 16,
              overflow: 'hidden',
            }}>
              <Image
                source={require("../../assets/images/LOGO.png")}
                style={{
                  width: 100,
                  height: 100,
                  borderRadius: 50,
                }}
                resizeMode="cover"
              />
            </View>
            <Text style={{
              fontSize: 32,
              fontWeight: 'bold',
              color: '#FFFFFF',
              textAlign: 'center',
              marginBottom: 8,
            }}>
              IraChat
            </Text>
            <Text style={{
              fontSize: 16,
              color: 'rgba(255, 255, 255, 0.9)',
              textAlign: 'center',
            }}>
              Connect with friends and family
            </Text>
          </View>

          {/* Email Registration Benefits */}
          <View style={{
            marginBottom: 40,
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            borderRadius: 16,
            padding: 24,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 5,
          }}>
            <View style={{ alignItems: 'center', marginBottom: 20 }}>
              <Ionicons name="mail-open" size={48} color="#87CEEB" style={{ marginBottom: 12 }} />
              <Text style={{
                fontSize: 22,
                fontWeight: '700',
                color: '#1F2937',
                textAlign: 'center',
                marginBottom: 8,
              }}>
                📧 Email Registration
              </Text>
              <Text style={{
                fontSize: 14,
                color: '#6B7280',
                textAlign: 'center',
                lineHeight: 20,
              }}>
                Create your account using your email address
              </Text>
            </View>

            <View style={{ gap: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
                <Ionicons name="checkmark-circle" size={20} color="#10B981" style={{ marginRight: 12, marginTop: 2 }} />
                <Text style={{ fontSize: 14, color: '#374151', flex: 1, lineHeight: 20 }}>
                  <Text style={{ fontWeight: '600' }}>Easy Setup:</Text> Quick registration with just your email and password
                </Text>
              </View>

              <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
                <Ionicons name="checkmark-circle" size={20} color="#10B981" style={{ marginRight: 12, marginTop: 2 }} />
                <Text style={{ fontSize: 14, color: '#374151', flex: 1, lineHeight: 20 }}>
                  <Text style={{ fontWeight: '600' }}>Email Verification:</Text> Secure account verification via email
                </Text>
              </View>

              <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
                <Ionicons name="checkmark-circle" size={20} color="#10B981" style={{ marginRight: 12, marginTop: 2 }} />
                <Text style={{ fontSize: 14, color: '#374151', flex: 1, lineHeight: 20 }}>
                  <Text style={{ fontWeight: '600' }}>Password Recovery:</Text> Easy password reset via email
                </Text>
              </View>

              <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
                <Ionicons name="checkmark-circle" size={20} color="#10B981" style={{ marginRight: 12, marginTop: 2 }} />
                <Text style={{ fontSize: 14, color: '#374151', flex: 1, lineHeight: 20 }}>
                  <Text style={{ fontWeight: '600' }}>Cross-Platform:</Text> Access your account from any device
                </Text>
              </View>
            </View>
          </View>

          {/* Privacy Information */}
          <View style={{
            marginBottom: 40,
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            borderRadius: 16,
            padding: 24,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 5,
          }}>
            <View style={{ alignItems: 'center', marginBottom: 20 }}>
              <Ionicons name="shield-checkmark" size={48} color="#87CEEB" style={{ marginBottom: 12 }} />
              <Text style={{
                fontSize: 22,
                fontWeight: '700',
                color: '#1F2937',
                textAlign: 'center',
                marginBottom: 8,
              }}>
                🔒 Privacy & Security
              </Text>
              <Text style={{
                fontSize: 14,
                color: '#6B7280',
                textAlign: 'center',
                lineHeight: 20,
              }}>
                Your privacy and security are our top priorities
              </Text>
            </View>

            <View style={{ gap: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
                <Ionicons name="lock-closed" size={20} color="#3B82F6" style={{ marginRight: 12, marginTop: 2 }} />
                <Text style={{ fontSize: 14, color: '#374151', flex: 1, lineHeight: 20 }}>
                  <Text style={{ fontWeight: '600' }}>Secure Storage:</Text> Your email is encrypted and stored securely
                </Text>
              </View>

              <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
                <Ionicons name="eye-off" size={20} color="#3B82F6" style={{ marginRight: 12, marginTop: 2 }} />
                <Text style={{ fontSize: 14, color: '#374151', flex: 1, lineHeight: 20 }}>
                  <Text style={{ fontWeight: '600' }}>Private by Default:</Text> Your email stays private unless you choose to share
                </Text>
              </View>

              <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
                <Ionicons name="notifications-off" size={20} color="#3B82F6" style={{ marginRight: 12, marginTop: 2 }} />
                <Text style={{ fontSize: 14, color: '#374151', flex: 1, lineHeight: 20 }}>
                  <Text style={{ fontWeight: '600' }}>No Spam:</Text> We'll only send important account notifications
                </Text>
              </View>

              <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
                <Ionicons name="trash" size={20} color="#3B82F6" style={{ marginRight: 12, marginTop: 2 }} />
                <Text style={{ fontSize: 14, color: '#374151', flex: 1, lineHeight: 20 }}>
                  <Text style={{ fontWeight: '600' }}>Data Control:</Text> You can delete your account and data anytime
                </Text>
              </View>
            </View>
          </View>

          {/* Continue Button */}
          <TouchableOpacity
            style={{
              backgroundColor: '#FFFFFF',
              borderRadius: 12,
              paddingVertical: 16,
              paddingHorizontal: 24,
              alignItems: 'center',
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.1,
              shadowRadius: 4,
              elevation: 3,
              marginBottom: 20,
            }}
            onPress={handleContinue}
            disabled={isNavigating}
          >
            <Text style={{
              fontSize: 18,
              fontWeight: '600',
              color: '#87CEEB',
            }}>
              {isNavigating ? 'Loading...' : 'Continue with Email Registration'}
            </Text>
          </TouchableOpacity>

          {/* Alternative Options */}
          <View style={{ alignItems: 'center', marginBottom: 20 }}>
            <Text style={{
              fontSize: 14,
              color: 'rgba(255, 255, 255, 0.8)',
              marginBottom: 12,
            }}>
              Or register with
            </Text>
            
            <TouchableOpacity
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                borderRadius: 8,
                paddingHorizontal: 16,
                paddingVertical: 10,
              }}
              onPress={() => router.push('/(auth)/register-info')}
              disabled={isNavigating}
            >
              <Ionicons name="call" size={18} color="#FFFFFF" style={{ marginRight: 8 }} />
              <Text style={{
                fontSize: 14,
                color: '#FFFFFF',
                fontWeight: '500',
              }}>
                Phone Number
              </Text>
            </TouchableOpacity>
          </View>

          {/* Sign In Link */}
          <View style={{
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: 20,
          }}>
            <Text style={{
              fontSize: 14,
              color: 'rgba(255, 255, 255, 0.8)',
            }}>
              Already have an account? 
            </Text>
            <TouchableOpacity 
              onPress={() => router.push('/(auth)/email-sign-in')}
              disabled={isNavigating}
            >
              <Text style={{
                fontSize: 14,
                color: '#FFFFFF',
                fontWeight: '600',
                marginLeft: 4,
                textDecorationLine: 'underline',
              }}>
                Sign In
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
